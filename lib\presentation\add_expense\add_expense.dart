
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import './widgets/advanced_options_widget.dart';
import './widgets/expense_form_widget.dart';
import './widgets/group_selection_widget.dart';
import './widgets/member_splitting_widget.dart';
import './widgets/receipt_section_widget.dart';

class AddExpense extends StatefulWidget {
  const AddExpense({Key? key}) : super(key: key);

  @override
  State<AddExpense> createState() => _AddExpenseState();
}

class _AddExpenseState extends State<AddExpense> {
  final ScrollController _scrollController = ScrollController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  bool _isLoading = false;
  bool _isDraftSaved = false;

  // Form Data
  Map<String, dynamic> _formData = {
    'description': '',
    'amount': 0.0,
    'category': 'Food',
    'currency': 'USD',
    'date': DateTime.now(),
    'notes': '',
  };

  Map<String, dynamic>? _selectedGroup;
  List<XFile> _receipts = [];

  Map<String, dynamic> _splitData = {
    'splitType': 'equal',
    'selectedMembers': {},
    'memberAmounts': {},
    'memberPercentages': {},
    'memberShares': {},
    'payerId': null,
  };

  Map<String, dynamic> _advancedOptions = {
    'tags': <String>[],
    'isRecurring': false,
    'recurringFrequency': 'monthly',
    'recurringEndDate': null,
  };

  // Currency conversion rates (mock data)
  final Map<String, double> _exchangeRates = {
    'USD': 1.0,
    'EUR': 0.85,
    'GBP': 0.73,
    'INR': 83.12,
    'CAD': 1.35,
    'AUD': 1.52,
    'JPY': 149.50,
  };

  @override
  void initState() {
    super.initState();
    _loadDraftData();
  }

  void _loadDraftData() {
    // In a real app, this would load from local storage
    // For now, we'll just initialize with default values
    setState(() {
      _isDraftSaved = false;
    });
  }

  void _saveDraft() {
    // In a real app, this would save to local storage
    setState(() {
      _isDraftSaved = true;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Draft saved automatically'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  bool _isFormValid() {
    return _formData['description'].toString().isNotEmpty &&
        _formData['amount'] > 0 &&
        _selectedGroup != null;
  }

  double _getConvertedAmount() {
    final amount = _formData['amount'] as double;
    final currency = _formData['currency'] as String;
    final rate = _exchangeRates[currency] ?? 1.0;
    return amount / rate; // Convert to USD base
  }

  void _showCurrencyInfo() {
    final currency = _formData['currency'] as String;
    final rate = _exchangeRates[currency] ?? 1.0;
    final lastUpdated = DateTime.now().subtract(Duration(minutes: 5));

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Currency Information'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Current Rate: 1 USD = ${rate.toStringAsFixed(4)} $currency'),
            SizedBox(height: 1.h),
            Text(
              'Last updated: ${lastUpdated.hour}:${lastUpdated.minute.toString().padLeft(2, '0')}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            if (currency != 'USD') ...[
              SizedBox(height: 1.h),
              Text(
                'Amount in USD: \$${_getConvertedAmount().toStringAsFixed(2)}',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('OK'),
          ),
        ],
      ),
    );
  }

  Future<void> _saveExpense() async {
    if (!_isFormValid()) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Please fill in all required fields'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      // Haptic feedback
      if (!kIsWeb) {
        HapticFeedback.mediumImpact();
      }

      // Simulate API call
      await Future.delayed(Duration(seconds: 2));

      // Create expense data
      final expenseData = {
        ..._formData,
        'group': _selectedGroup,
        'receipts': _receipts.map((receipt) => receipt.path).toList(),
        'splitData': _splitData,
        'advancedOptions': _advancedOptions,
        'convertedAmount': _getConvertedAmount(),
        'createdAt': DateTime.now().toIso8601String(),
      };

      // Success haptic feedback
      if (!kIsWeb) {
        HapticFeedback.lightImpact();
      }

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              CustomIconWidget(
                iconName: 'check_circle',
                color: Colors.white,
                size: 20,
              ),
              SizedBox(width: 2.w),
              Text('Expense added successfully!'),
            ],
          ),
          backgroundColor: Theme.of(context).brightness == Brightness.dark
              ? AppTheme.successDark
              : AppTheme.successLight,
        ),
      );

      // Navigate back with result
      Navigator.pop(context, expenseData);
    } catch (e) {
      // Error haptic feedback
      if (!kIsWeb) {
        HapticFeedback.heavyImpact();
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to save expense. Please try again.'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor:
          isDark ? AppTheme.backgroundDark : AppTheme.backgroundLight,
      appBar: AppBar(
        title: Text('Add Expense'),
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: CustomIconWidget(
            iconName: 'close',
            color: isDark ? AppTheme.onSurfaceDark : AppTheme.onSurfaceLight,
            size: 24,
          ),
        ),
        actions: [
          if (_isDraftSaved)
            Padding(
              padding: EdgeInsets.only(right: 2.w),
              child: Center(
                child: Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 3.w, vertical: 0.5.h),
                  decoration: BoxDecoration(
                    color:
                        isDark ? AppTheme.successDark : AppTheme.successLight,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'Draft Saved',
                    style: TextStyle(
                      fontSize: 10.sp,
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ),
          TextButton(
            onPressed: _isFormValid() && !_isLoading ? _saveExpense : null,
            child: _isLoading
                ? SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
                      ),
                    ),
                  )
                : Text(
                    'Save',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: _isFormValid()
                          ? (isDark
                              ? AppTheme.primaryDark
                              : AppTheme.primaryLight)
                          : (isDark
                              ? AppTheme.onSurfaceVariantDark
                              : AppTheme.onSurfaceVariantLight),
                    ),
                  ),
          ),
          SizedBox(width: 4.w),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          controller: _scrollController,
          padding: EdgeInsets.all(4.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Currency Info Banner
              if (_formData['currency'] != 'USD')
                Container(
                  margin: EdgeInsets.only(bottom: 3.h),
                  padding: EdgeInsets.all(3.w),
                  decoration: BoxDecoration(
                    color: isDark
                        ? AppTheme.primaryDark.withValues(alpha: 0.1)
                        : AppTheme.primaryLight.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color:
                          isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      CustomIconWidget(
                        iconName: 'info',
                        color: isDark
                            ? AppTheme.primaryDark
                            : AppTheme.primaryLight,
                        size: 20,
                      ),
                      SizedBox(width: 3.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Currency Conversion',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                            ),
                            Text(
                              'Amount will be converted to USD: \$${_getConvertedAmount().toStringAsFixed(2)}',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                          ],
                        ),
                      ),
                      TextButton(
                        onPressed: _showCurrencyInfo,
                        child: Text('Details'),
                      ),
                    ],
                  ),
                ),

              // Main Form
              ExpenseFormWidget(
                formData: _formData,
                onFormChanged: (data) {
                  setState(() => _formData = data);
                  _saveDraft();
                },
              ),

              SizedBox(height: 3.h),

              // Receipt Section
              ReceiptSectionWidget(
                receipts: _receipts,
                onReceiptsChanged: (receipts) {
                  setState(() => _receipts = receipts);
                  _saveDraft();
                },
              ),

              SizedBox(height: 3.h),

              // Group Selection
              GroupSelectionWidget(
                selectedGroup: _selectedGroup,
                onGroupChanged: (group) {
                  setState(() => _selectedGroup = group);
                  _saveDraft();
                },
              ),

              SizedBox(height: 3.h),

              // Member Splitting
              MemberSplittingWidget(
                selectedGroup: _selectedGroup,
                splitData: _splitData,
                onSplitChanged: (data) {
                  setState(() => _splitData = data);
                  _saveDraft();
                },
              ),

              SizedBox(height: 3.h),

              // Advanced Options
              AdvancedOptionsWidget(
                optionsData: _advancedOptions,
                onOptionsChanged: (data) {
                  setState(() => _advancedOptions = data);
                  _saveDraft();
                },
              ),

              SizedBox(height: 4.h),

              // Save Button (Mobile)
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed:
                      _isFormValid() && !_isLoading ? _saveExpense : null,
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.symmetric(vertical: 2.h),
                    backgroundColor: _isFormValid()
                        ? (isDark
                            ? AppTheme.primaryDark
                            : AppTheme.primaryLight)
                        : (isDark
                            ? AppTheme.surfaceVariantDark
                            : AppTheme.surfaceVariantLight),
                  ),
                  child: _isLoading
                      ? Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            ),
                            SizedBox(width: 3.w),
                            Text('Saving...'),
                          ],
                        )
                      : Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CustomIconWidget(
                              iconName: 'save',
                              color: Colors.white,
                              size: 20,
                            ),
                            SizedBox(width: 2.w),
                            Text(
                              'Save Expense',
                              style: TextStyle(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                ),
              ),

              SizedBox(height: 2.h),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
}