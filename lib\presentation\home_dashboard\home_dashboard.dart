import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import './widgets/active_groups_widget.dart';
import './widgets/floating_action_menu_widget.dart';
import './widgets/quick_actions_widget.dart';
import './widgets/recent_activity_widget.dart';
import './widgets/user_header_widget.dart';

class HomeDashboard extends StatefulWidget {
  const HomeDashboard({Key? key}) : super(key: key);

  @override
  State<HomeDashboard> createState() => _HomeDashboardState();
}

class _HomeDashboardState extends State<HomeDashboard> {
  bool _isRefreshing = false;

  // Mock data for recent activities
  final List<Map<String, dynamic>> _recentActivities = [
    {
      "id": 1,
      "type": "expense",
      "description": "Dinner at Italian Restaurant",
      "amount": "\$85.50",
      "currency": "USD",
      "groupName": "Weekend Trip",
      "timestamp": DateTime.now().subtract(const Duration(hours: 2)),
    },
    {
      "id": 2,
      "type": "settlement",
      "description": "Payment received from <PERSON>",
      "amount": "\$42.25",
      "currency": "USD",
      "groupName": "Apartment Expenses",
      "timestamp": DateTime.now().subtract(const Duration(hours: 5)),
    },
    {
      "id": 3,
      "type": "expense",
      "description": "Uber ride to airport",
      "amount": "\$28.75",
      "currency": "USD",
      "groupName": "Business Trip",
      "timestamp": DateTime.now().subtract(const Duration(days: 1)),
    },
    {
      "id": 4,
      "type": "group_created",
      "description": "Created new group 'Vacation 2024'",
      "amount": "\$0.00",
      "currency": "USD",
      "groupName": "Vacation 2024",
      "timestamp": DateTime.now().subtract(const Duration(days: 2)),
    },
    {
      "id": 5,
      "type": "member_added",
      "description": "Sarah joined the group",
      "amount": "\$0.00",
      "currency": "USD",
      "groupName": "Weekend Trip",
      "timestamp": DateTime.now().subtract(const Duration(days: 3)),
    },
  ];

  // Mock data for active groups
  final List<Map<String, dynamic>> _activeGroups = [
    {
      "id": 1,
      "name": "Weekend Trip",
      "coverImage":
          "https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=500&h=300&fit=crop",
      "memberCount": 4,
      "totalExpenses": "\$324.75",
      "userBalance": 45.50,
      "lastActivity": DateTime.now().subtract(const Duration(hours: 2)),
    },
    {
      "id": 2,
      "name": "Apartment Expenses",
      "coverImage":
          "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=500&h=300&fit=crop",
      "memberCount": 3,
      "totalExpenses": "\$1,250.00",
      "userBalance": -125.25,
      "lastActivity": DateTime.now().subtract(const Duration(hours: 5)),
    },
    {
      "id": 3,
      "name": "Business Trip",
      "coverImage":
          "https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=500&h=300&fit=crop",
      "memberCount": 2,
      "totalExpenses": "\$892.30",
      "userBalance": 0.0,
      "lastActivity": DateTime.now().subtract(const Duration(days: 1)),
    },
    {
      "id": 4,
      "name": "Vacation 2024",
      "coverImage":
          "https://images.unsplash.com/photo-1507525428034-b723cf961d3e?w=500&h=300&fit=crop",
      "memberCount": 6,
      "totalExpenses": "\$2,150.80",
      "userBalance": 320.15,
      "lastActivity": DateTime.now().subtract(const Duration(days: 2)),
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: _handleRefresh,
        color: AppTheme.lightTheme.primaryColor,
        child: CustomScrollView(
          slivers: [
            // User Header
            SliverToBoxAdapter(
              child: UserHeaderWidget(
                userName: "Alex Johnson",
                userAvatar:
                    "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
                notificationCount: 3,
                onNotificationTap: _handleNotificationTap,
              ),
            ),

            // Recent Activity
            SliverToBoxAdapter(
              child: RecentActivityWidget(
                activities: _recentActivities,
                onActivityTap: _handleActivityTap,
                onEditActivity: _handleEditActivity,
                onDeleteActivity: _handleDeleteActivity,
                onShareActivity: _handleShareActivity,
              ),
            ),

            // Active Groups
            SliverToBoxAdapter(
              child: ActiveGroupsWidget(
                groups: _activeGroups,
                onGroupTap: _handleGroupTap,
                onGroupLongPress: _handleGroupLongPress,
              ),
            ),

            // Quick Actions
            SliverToBoxAdapter(
              child: QuickActionsWidget(
                onAddExpense: _handleAddExpense,
                onCreateGroup: _handleCreateGroup,
                onViewExpenses: _handleViewExpenses,
                onSettleUp: _handleSettleUp,
              ),
            ),

            // Bottom spacing for FAB
            SliverToBoxAdapter(
              child: SizedBox(height: 20.h),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionMenuWidget(
        onAddExpense: _handleAddExpense,
        onCreateGroup: _handleCreateGroup,
        onScanReceipt: _handleScanReceipt,
      ),
    );
  }

  Future<void> _handleRefresh() async {
    setState(() {
      _isRefreshing = true;
    });

    // Simulate API call
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _isRefreshing = false;
    });

    // Show success feedback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Data refreshed successfully'),
        backgroundColor: AppTheme.successLight,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _handleNotificationTap() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.all(4.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 12.w,
              height: 0.5.h,
              decoration: BoxDecoration(
                color: Theme.of(context).dividerColor,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            SizedBox(height: 2.h),
            Text(
              'Notifications',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
            SizedBox(height: 3.h),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'receipt',
                color: AppTheme.lightTheme.primaryColor,
                size: 24,
              ),
              title: Text('New expense added'),
              subtitle: Text('John added \$25.50 for lunch'),
              trailing: Text('2h ago'),
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'account_balance_wallet',
                color: AppTheme.successLight,
                size: 24,
              ),
              title: Text('Payment received'),
              subtitle: Text('Sarah paid you \$42.25'),
              trailing: Text('5h ago'),
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'group_add',
                color: AppTheme.secondaryLight,
                size: 24,
              ),
              title: Text('Group invitation'),
              subtitle: Text('Mike invited you to "Office Party"'),
              trailing: Text('1d ago'),
            ),
            SizedBox(height: 2.h),
          ],
        ),
      ),
    );
  }

  void _handleActivityTap(Map<String, dynamic> activity) {
    Navigator.pushNamed(context, '/expenses-list');
  }

  void _handleEditActivity(Map<String, dynamic> activity) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Edit activity: ${activity['description']}'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _handleDeleteActivity(Map<String, dynamic> activity) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Delete Activity'),
        content: Text(
            'Are you sure you want to delete "${activity['description']}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _recentActivities
                    .removeWhere((item) => item['id'] == activity['id']);
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Activity deleted'),
                  backgroundColor: AppTheme.successLight,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorLight,
            ),
            child: Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _handleShareActivity(Map<String, dynamic> activity) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Share activity: ${activity['description']}'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _handleGroupTap(Map<String, dynamic> group) {
    Navigator.pushNamed(context, '/group-details');
  }

  void _handleGroupLongPress(Map<String, dynamic> group) {
    // This is handled in the ActiveGroupsWidget
  }

  void _handleAddExpense() {
    Navigator.pushNamed(context, '/add-expense');
  }

  void _handleCreateGroup() {
    Navigator.pushNamed(context, '/groups-list');
  }

  void _handleViewExpenses() {
    Navigator.pushNamed(context, '/expenses-list');
  }

  void _handleSettleUp() {
    Navigator.pushNamed(context, '/settlement-screen');
  }

  void _handleScanReceipt() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening camera to scan receipt...'),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
