import 'dart:io' if (dart.library.io) 'dart:io';

import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class ReceiptSectionWidget extends StatefulWidget {
  final Function(List<XFile>) onReceiptsChanged;
  final List<XFile> receipts;

  const ReceiptSectionWidget({
    Key? key,
    required this.onReceiptsChanged,
    required this.receipts,
  }) : super(key: key);

  @override
  State<ReceiptSectionWidget> createState() => _ReceiptSectionWidgetState();
}

class _ReceiptSectionWidgetState extends State<ReceiptSectionWidget> {
  CameraController? _cameraController;
  List<CameraDescription> _cameras = [];
  bool _isCameraInitialized = false;
  bool _showCamera = false;
  final ImagePicker _imagePicker = ImagePicker();

  @override
  void initState() {
    super.initState();
    _initializeCamera();
  }

  Future<void> _initializeCamera() async {
    try {
      if (!await _requestCameraPermission()) return;

      _cameras = await availableCameras();
      if (_cameras.isEmpty) return;

      final camera = kIsWeb
          ? _cameras.firstWhere(
              (c) => c.lensDirection == CameraLensDirection.front,
              orElse: () => _cameras.first)
          : _cameras.firstWhere(
              (c) => c.lensDirection == CameraLensDirection.back,
              orElse: () => _cameras.first);

      _cameraController = CameraController(
          camera, kIsWeb ? ResolutionPreset.medium : ResolutionPreset.high);

      await _cameraController!.initialize();
      await _applySettings();

      if (mounted) {
        setState(() => _isCameraInitialized = true);
      }
    } catch (e) {
      debugPrint('Camera initialization error: $e');
    }
  }

  Future<bool> _requestCameraPermission() async {
    if (kIsWeb) return true;
    return (await Permission.camera.request()).isGranted;
  }

  Future<void> _applySettings() async {
    if (_cameraController == null) return;

    try {
      await _cameraController!.setFocusMode(FocusMode.auto);
      if (!kIsWeb) {
        try {
          await _cameraController!.setFlashMode(FlashMode.auto);
        } catch (e) {
          debugPrint('Flash mode not supported: $e');
        }
      }
    } catch (e) {
      debugPrint('Camera settings error: $e');
    }
  }

  Future<void> _capturePhoto() async {
    if (_cameraController == null || !_cameraController!.value.isInitialized) {
      return;
    }

    try {
      final XFile photo = await _cameraController!.takePicture();
      final updatedReceipts = [...widget.receipts, photo];
      widget.onReceiptsChanged(updatedReceipts);
      setState(() => _showCamera = false);
    } catch (e) {
      debugPrint('Photo capture error: $e');
    }
  }

  Future<void> _pickFromGallery() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        final updatedReceipts = [...widget.receipts, image];
        widget.onReceiptsChanged(updatedReceipts);
      }
    } catch (e) {
      debugPrint('Gallery picker error: $e');
    }
  }

  void _removeReceipt(int index) {
    final updatedReceipts = [...widget.receipts];
    updatedReceipts.removeAt(index);
    widget.onReceiptsChanged(updatedReceipts);
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Receipt (Optional)',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        SizedBox(height: 1.h),
        if (_showCamera && _isCameraInitialized) ...[
          Container(
            height: 40.h,
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
                width: 2,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: Stack(
                children: [
                  CameraPreview(_cameraController!),
                  Positioned(
                    top: 2.h,
                    right: 4.w,
                    child: GestureDetector(
                      onTap: () => setState(() => _showCamera = false),
                      child: Container(
                        padding: EdgeInsets.all(2.w),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.5),
                          shape: BoxShape.circle,
                        ),
                        child: CustomIconWidget(
                          iconName: 'close',
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                    ),
                  ),
                  Positioned(
                    bottom: 3.h,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: GestureDetector(
                        onTap: _capturePhoto,
                        child: Container(
                          width: 20.w,
                          height: 20.w,
                          decoration: BoxDecoration(
                            color: isDark
                                ? AppTheme.primaryDark
                                : AppTheme.primaryLight,
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.white, width: 3),
                          ),
                          child: CustomIconWidget(
                            iconName: 'camera_alt',
                            color: Colors.white,
                            size: 30,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: 2.h),
        ] else ...[
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => setState(() => _showCamera = true),
                  icon: CustomIconWidget(
                    iconName: 'camera_alt',
                    color:
                        isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
                    size: 20,
                  ),
                  label: Text('Take Photo'),
                ),
              ),
              SizedBox(width: 3.w),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _pickFromGallery,
                  icon: CustomIconWidget(
                    iconName: 'photo_library',
                    color:
                        isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
                    size: 20,
                  ),
                  label: Text('Gallery'),
                ),
              ),
            ],
          ),
        ],
        if (widget.receipts.isNotEmpty) ...[
          SizedBox(height: 2.h),
          Text(
            'Receipts (${widget.receipts.length})',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
          ),
          SizedBox(height: 1.h),
          SizedBox(
            height: 12.h,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: widget.receipts.length,
              itemBuilder: (context, index) {
                final receipt = widget.receipts[index];
                return Container(
                  margin: EdgeInsets.only(right: 3.w),
                  child: Stack(
                    children: [
                      Container(
                        width: 20.w,
                        height: 12.h,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: isDark
                                ? AppTheme.outlineDark
                                : AppTheme.outlineLight,
                          ),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(7),
                          child: kIsWeb
                              ? Image.network(
                                  receipt.path,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Container(
                                      color: isDark
                                          ? AppTheme.surfaceVariantDark
                                          : AppTheme.surfaceVariantLight,
                                      child: Center(
                                        child: CustomIconWidget(
                                          iconName: 'receipt',
                                          color: isDark
                                              ? AppTheme.onSurfaceVariantDark
                                              : AppTheme.onSurfaceVariantLight,
                                          size: 24,
                                        ),
                                      ),
                                    );
                                  },
                                )
                              : Image.file(
                                  File(receipt.path),
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Container(
                                      color: isDark
                                          ? AppTheme.surfaceVariantDark
                                          : AppTheme.surfaceVariantLight,
                                      child: Center(
                                        child: CustomIconWidget(
                                          iconName: 'receipt',
                                          color: isDark
                                              ? AppTheme.onSurfaceVariantDark
                                              : AppTheme.onSurfaceVariantLight,
                                          size: 24,
                                        ),
                                      ),
                                    );
                                  },
                                ),
                        ),
                      ),
                      Positioned(
                        top: 0.5.h,
                        right: 1.w,
                        child: GestureDetector(
                          onTap: () => _removeReceipt(index),
                          child: Container(
                            padding: EdgeInsets.all(1.w),
                            decoration: BoxDecoration(
                              color: isDark
                                  ? AppTheme.errorDark
                                  : AppTheme.errorLight,
                              shape: BoxShape.circle,
                            ),
                            child: CustomIconWidget(
                              iconName: 'close',
                              color: Colors.white,
                              size: 12,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ],
    );
  }

  @override
  void dispose() {
    _cameraController?.dispose();
    super.dispose();
  }
}
