import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import './widgets/empty_state_widget.dart';
import './widgets/expense_card_widget.dart';
import './widgets/filter_bottom_sheet_widget.dart';
import './widgets/monthly_section_header_widget.dart';
import './widgets/search_bar_widget.dart';
import './widgets/sort_bottom_sheet_widget.dart';

class ExpensesList extends StatefulWidget {
  const ExpensesList({Key? key}) : super(key: key);

  @override
  State<ExpensesList> createState() => _ExpensesListState();
}

class _ExpensesListState extends State<ExpensesList>
    with TickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  List<Map<String, dynamic>> _allExpenses = [];
  List<Map<String, dynamic>> _filteredExpenses = [];
  Map<String, dynamic> _currentFilters = {};
  String _currentSortBy = 'date';
  bool _currentSortAscending = false;
  bool _isLoading = false;
  bool _isMultiSelectMode = false;
  Set<String> _selectedExpenseIds = {};
  int _currentBottomNavIndex = 2; // Expenses tab active

  @override
  void initState() {
    super.initState();
    _initializeMockData();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _initializeMockData() {
    _allExpenses = [
      {
        "id": "1",
        "description": "Dinner at Italian Restaurant",
        "amount": 85.50,
        "currency": "\$",
        "groupName": "Trip to Paris",
        "date": DateTime.now().subtract(const Duration(days: 1)),
        "category": "food",
        "payer": "John Smith",
        "userShare": 21.38,
        "isPaid": false,
      },
      {
        "id": "2",
        "description": "Uber to Airport",
        "amount": 45.00,
        "currency": "\$",
        "groupName": "Trip to Paris",
        "date": DateTime.now().subtract(const Duration(days: 2)),
        "category": "transport",
        "payer": "Sarah Johnson",
        "userShare": 15.00,
        "isPaid": true,
      },
      {
        "id": "3",
        "description": "Hotel Booking - 3 nights",
        "amount": 320.00,
        "currency": "\$",
        "groupName": "Weekend Getaway",
        "date": DateTime.now().subtract(const Duration(days: 5)),
        "category": "accommodation",
        "payer": "Mike Wilson",
        "userShare": 80.00,
        "isPaid": false,
      },
      {
        "id": "4",
        "description": "Movie Tickets",
        "amount": 48.00,
        "currency": "\$",
        "groupName": "Weekend Getaway",
        "date": DateTime.now().subtract(const Duration(days: 7)),
        "category": "entertainment",
        "payer": "Emma Davis",
        "userShare": 12.00,
        "isPaid": true,
      },
      {
        "id": "5",
        "description": "Grocery Shopping",
        "amount": 125.75,
        "currency": "\$",
        "groupName": "Apartment Rent",
        "date": DateTime.now().subtract(const Duration(days: 10)),
        "category": "shopping",
        "payer": "Alex Brown",
        "userShare": 31.44,
        "isPaid": false,
      },
      {
        "id": "6",
        "description": "Electricity Bill",
        "amount": 89.20,
        "currency": "\$",
        "groupName": "Apartment Rent",
        "date": DateTime.now().subtract(const Duration(days: 15)),
        "category": "utilities",
        "payer": "Lisa Garcia",
        "userShare": 22.30,
        "isPaid": true,
      },
      {
        "id": "7",
        "description": "Team Lunch",
        "amount": 156.80,
        "currency": "\$",
        "groupName": "Office Lunch",
        "date": DateTime.now().subtract(const Duration(days: 20)),
        "category": "food",
        "payer": "David Lee",
        "userShare": 26.13,
        "isPaid": false,
      },
      {
        "id": "8",
        "description": "Birthday Cake & Decorations",
        "amount": 67.45,
        "currency": "\$",
        "groupName": "Birthday Party",
        "date": DateTime.now().subtract(const Duration(days: 25)),
        "category": "shopping",
        "payer": "Rachel Kim",
        "userShare": 13.49,
        "isPaid": true,
      },
    ];

    _applyFiltersAndSort();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreExpenses();
    }
  }

  Future<void> _loadMoreExpenses() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    // Simulate loading more expenses
    await Future.delayed(const Duration(seconds: 1));

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _refreshExpenses() async {
    setState(() {
      _isLoading = true;
    });

    // Simulate refresh
    await Future.delayed(const Duration(seconds: 1));

    setState(() {
      _isLoading = false;
    });
  }

  void _onSearchChanged(String query) {
    _applyFiltersAndSort();
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => FilterBottomSheetWidget(
        currentFilters: _currentFilters,
        onFiltersApplied: (filters) {
          setState(() {
            _currentFilters = filters;
          });
          _applyFiltersAndSort();
        },
      ),
    );
  }

  void _showSortBottomSheet() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => SortBottomSheetWidget(
        currentSortBy: _currentSortBy,
        currentSortAscending: _currentSortAscending,
        onSortChanged: (sortBy, ascending) {
          setState(() {
            _currentSortBy = sortBy;
            _currentSortAscending = ascending;
          });
          _applyFiltersAndSort();
        },
      ),
    );
  }

  void _applyFiltersAndSort() {
    List<Map<String, dynamic>> filtered = List.from(_allExpenses);

    // Apply search filter
    final searchQuery = _searchController.text.toLowerCase();
    if (searchQuery.isNotEmpty) {
      filtered = filtered.where((expense) {
        final description = (expense['description'] as String).toLowerCase();
        final groupName = (expense['groupName'] as String).toLowerCase();
        final amount = expense['amount'].toString();
        return description.contains(searchQuery) ||
            groupName.contains(searchQuery) ||
            amount.contains(searchQuery);
      }).toList();
    }

    // Apply filters
    if (_currentFilters['dateRange'] != null) {
      final dateRange = _currentFilters['dateRange'] as DateTimeRange;
      filtered = filtered.where((expense) {
        final expenseDate = expense['date'] as DateTime;
        return expenseDate
                .isAfter(dateRange.start.subtract(const Duration(days: 1))) &&
            expenseDate.isBefore(dateRange.end.add(const Duration(days: 1)));
      }).toList();
    }

    if (_currentFilters['amountRange'] != null) {
      final amountRange = _currentFilters['amountRange'] as RangeValues;
      filtered = filtered.where((expense) {
        final amount = expense['amount'] as double;
        return amount >= amountRange.start && amount <= amountRange.end;
      }).toList();
    }

    if (_currentFilters['groups'] != null &&
        (_currentFilters['groups'] as List).isNotEmpty) {
      final selectedGroups = _currentFilters['groups'] as List<String>;
      filtered = filtered.where((expense) {
        return selectedGroups.contains(expense['groupName']);
      }).toList();
    }

    if (_currentFilters['categories'] != null &&
        (_currentFilters['categories'] as List).isNotEmpty) {
      final selectedCategories = _currentFilters['categories'] as List<String>;
      filtered = filtered.where((expense) {
        return selectedCategories
            .map((c) => c.toLowerCase())
            .contains((expense['category'] as String).toLowerCase());
      }).toList();
    }

    if (_currentFilters['paymentStatus'] != null &&
        _currentFilters['paymentStatus'] != 'all') {
      final isPaidFilter = _currentFilters['paymentStatus'] == 'paid';
      filtered = filtered.where((expense) {
        return expense['isPaid'] == isPaidFilter;
      }).toList();
    }

    // Apply sorting
    filtered.sort((a, b) {
      int comparison = 0;
      switch (_currentSortBy) {
        case 'date':
          comparison = (a['date'] as DateTime).compareTo(b['date'] as DateTime);
          break;
        case 'amount':
          comparison = (a['amount'] as double).compareTo(b['amount'] as double);
          break;
        case 'group':
          comparison =
              (a['groupName'] as String).compareTo(b['groupName'] as String);
          break;
        case 'category':
          comparison =
              (a['category'] as String).compareTo(b['category'] as String);
          break;
      }
      return _currentSortAscending ? comparison : -comparison;
    });

    setState(() {
      _filteredExpenses = filtered;
    });
  }

  void _toggleMultiSelectMode() {
    setState(() {
      _isMultiSelectMode = !_isMultiSelectMode;
      if (!_isMultiSelectMode) {
        _selectedExpenseIds.clear();
      }
    });
  }

  void _toggleExpenseSelection(String expenseId) {
    setState(() {
      if (_selectedExpenseIds.contains(expenseId)) {
        _selectedExpenseIds.remove(expenseId);
      } else {
        _selectedExpenseIds.add(expenseId);
      }
    });
  }

  void _onExpenseLongPress(String expenseId) {
    if (!_isMultiSelectMode) {
      _toggleMultiSelectMode();
    }
    _toggleExpenseSelection(expenseId);
  }

  void _deleteSelectedExpenses() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Expenses'),
        content: Text(
            'Are you sure you want to delete ${_selectedExpenseIds.length} expense(s)?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _allExpenses.removeWhere(
                    (expense) => _selectedExpenseIds.contains(expense['id']));
                _selectedExpenseIds.clear();
                _isMultiSelectMode = false;
              });
              _applyFiltersAndSort();
              Navigator.pop(context);
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _exportSelectedExpenses() {
    // Export functionality would be implemented here
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Export functionality coming soon')),
    );
  }

  Map<String, List<Map<String, dynamic>>> _groupExpensesByMonth() {
    final Map<String, List<Map<String, dynamic>>> grouped = {};

    for (final expense in _filteredExpenses) {
      final date = expense['date'] as DateTime;
      final monthYear = '${_getMonthName(date.month)} ${date.year}';

      if (!grouped.containsKey(monthYear)) {
        grouped[monthYear] = [];
      }
      grouped[monthYear]!.add(expense);
    }

    return grouped;
  }

  String _getMonthName(int month) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];
    return months[month - 1];
  }

  double _calculateMonthlyTotal(List<Map<String, dynamic>> expenses) {
    return expenses.fold(
        0.0, (sum, expense) => sum + (expense['amount'] as double));
  }

  @override
  Widget build(BuildContext context) {
    final bool isDark = Theme.of(context).brightness == Brightness.dark;
    final groupedExpenses = _groupExpensesByMonth();

    return Scaffold(
      backgroundColor:
          isDark ? AppTheme.backgroundDark : AppTheme.backgroundLight,
      body: Column(
        children: [
          // Sticky Header with Search Bar
          SearchBarWidget(
            controller: _searchController,
            onChanged: _onSearchChanged,
            onFilterTap: _showFilterBottomSheet,
            onSortTap: _showSortBottomSheet,
          ),

          // Multi-select toolbar
          if (_isMultiSelectMode) ...[
            Container(
              padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
              decoration: BoxDecoration(
                color: isDark
                    ? AppTheme.primaryDark.withValues(alpha: 0.1)
                    : AppTheme.primaryLight.withValues(alpha: 0.1),
                border: Border(
                  bottom: BorderSide(
                    color:
                        isDark ? AppTheme.outlineDark : AppTheme.outlineLight,
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Text(
                    '${_selectedExpenseIds.length} selected',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: isDark
                              ? AppTheme.primaryDark
                              : AppTheme.primaryLight,
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: _exportSelectedExpenses,
                    icon: CustomIconWidget(
                      iconName: 'file_download',
                      color:
                          isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
                      size: 6.w,
                    ),
                  ),
                  IconButton(
                    onPressed: _deleteSelectedExpenses,
                    icon: CustomIconWidget(
                      iconName: 'delete',
                      color: AppTheme.errorLight,
                      size: 6.w,
                    ),
                  ),
                  IconButton(
                    onPressed: _toggleMultiSelectMode,
                    icon: CustomIconWidget(
                      iconName: 'close',
                      color: isDark
                          ? AppTheme.onSurfaceVariantDark
                          : AppTheme.onSurfaceVariantLight,
                      size: 6.w,
                    ),
                  ),
                ],
              ),
            ),
          ],

          // Main Content
          Expanded(
            child: _filteredExpenses.isEmpty
                ? EmptyStateWidget(
                    onAddExpense: () {
                      Navigator.pushNamed(context, '/add-expense');
                    },
                  )
                : RefreshIndicator(
                    onRefresh: _refreshExpenses,
                    color:
                        isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
                    child: ListView.builder(
                      controller: _scrollController,
                      padding: EdgeInsets.only(bottom: 10.h),
                      itemCount: _calculateListItemCount(groupedExpenses),
                      itemBuilder: (context, index) {
                        return _buildListItem(groupedExpenses, index);
                      },
                    ),
                  ),
          ),
        ],
      ),

      // Bottom Navigation Bar
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentBottomNavIndex,
        type: BottomNavigationBarType.fixed,
        onTap: (index) {
          setState(() {
            _currentBottomNavIndex = index;
          });
          _navigateToTab(index);
        },
        items: [
          BottomNavigationBarItem(
            icon: CustomIconWidget(
              iconName: 'dashboard',
              color: _currentBottomNavIndex == 0
                  ? (isDark ? AppTheme.primaryDark : AppTheme.primaryLight)
                  : (isDark
                      ? AppTheme.onSurfaceVariantDark
                      : AppTheme.onSurfaceVariantLight),
              size: 6.w,
            ),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: CustomIconWidget(
              iconName: 'group',
              color: _currentBottomNavIndex == 1
                  ? (isDark ? AppTheme.primaryDark : AppTheme.primaryLight)
                  : (isDark
                      ? AppTheme.onSurfaceVariantDark
                      : AppTheme.onSurfaceVariantLight),
              size: 6.w,
            ),
            label: 'Groups',
          ),
          BottomNavigationBarItem(
            icon: CustomIconWidget(
              iconName: 'receipt_long',
              color: _currentBottomNavIndex == 2
                  ? (isDark ? AppTheme.primaryDark : AppTheme.primaryLight)
                  : (isDark
                      ? AppTheme.onSurfaceVariantDark
                      : AppTheme.onSurfaceVariantLight),
              size: 6.w,
            ),
            label: 'Expenses',
          ),
          BottomNavigationBarItem(
            icon: CustomIconWidget(
              iconName: 'person',
              color: _currentBottomNavIndex == 3
                  ? (isDark ? AppTheme.primaryDark : AppTheme.primaryLight)
                  : (isDark
                      ? AppTheme.onSurfaceVariantDark
                      : AppTheme.onSurfaceVariantLight),
              size: 6.w,
            ),
            label: 'Profile',
          ),
        ],
      ),

      // Floating Action Button
      floatingActionButton: _isMultiSelectMode
          ? null
          : FloatingActionButton(
              onPressed: () {
                Navigator.pushNamed(context, '/add-expense');
              },
              child: CustomIconWidget(
                iconName: 'add',
                color: Colors.white,
                size: 7.w,
              ),
            ),
    );
  }

  int _calculateListItemCount(
      Map<String, List<Map<String, dynamic>>> groupedExpenses) {
    int count = 0;
    for (final entry in groupedExpenses.entries) {
      count += 1 + entry.value.length; // 1 for header + expenses count
    }
    if (_isLoading) count += 1; // Loading indicator
    return count;
  }

  Widget _buildListItem(
      Map<String, List<Map<String, dynamic>>> groupedExpenses, int index) {
    int currentIndex = 0;

    for (final entry in groupedExpenses.entries) {
      final monthYear = entry.key;
      final expenses = entry.value;

      // Monthly header
      if (currentIndex == index) {
        return MonthlySectionHeaderWidget(
          monthYear: monthYear,
          totalAmount: _calculateMonthlyTotal(expenses),
          expenseCount: expenses.length,
        );
      }
      currentIndex++;

      // Expense cards
      for (int i = 0; i < expenses.length; i++) {
        if (currentIndex == index) {
          final expense = expenses[i];
          final expenseId = expense['id'] as String;
          final isSelected = _selectedExpenseIds.contains(expenseId);

          return GestureDetector(
            onLongPress: () => _onExpenseLongPress(expenseId),
            child: Container(
              decoration: _isMultiSelectMode && isSelected
                  ? BoxDecoration(
                      color: (Theme.of(context).brightness == Brightness.dark
                              ? AppTheme.primaryDark
                              : AppTheme.primaryLight)
                          .withValues(alpha: 0.1),
                      border: Border.all(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? AppTheme.primaryDark
                            : AppTheme.primaryLight,
                        width: 2,
                      ),
                    )
                  : null,
              child: ExpenseCardWidget(
                expense: expense,
                onTap: _isMultiSelectMode
                    ? () => _toggleExpenseSelection(expenseId)
                    : () {
                        Navigator.pushNamed(context, '/group-details');
                      },
                onEdit: () {
                  Navigator.pushNamed(context, '/add-expense');
                },
                onDelete: () {
                  _showDeleteConfirmation(expense);
                },
              ),
            ),
          );
        }
        currentIndex++;
      }
    }

    // Loading indicator
    if (_isLoading && currentIndex == index) {
      return Container(
        padding: EdgeInsets.all(4.w),
        child: Center(
          child: CircularProgressIndicator(
            color: Theme.of(context).brightness == Brightness.dark
                ? AppTheme.primaryDark
                : AppTheme.primaryLight,
          ),
        ),
      );
    }

    return const SizedBox.shrink();
  }

  void _showDeleteConfirmation(Map<String, dynamic> expense) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Expense'),
        content: Text(
            'Are you sure you want to delete "${expense['description']}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _allExpenses.removeWhere((e) => e['id'] == expense['id']);
              });
              _applyFiltersAndSort();
              Navigator.pop(context);
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _navigateToTab(int index) {
    switch (index) {
      case 0:
        Navigator.pushNamed(context, '/home-dashboard');
        break;
      case 1:
        Navigator.pushNamed(context, '/groups-list');
        break;
      case 2:
        // Already on expenses list
        break;
      case 3:
        // Profile tab - would navigate to profile screen
        break;
    }
  }
}
