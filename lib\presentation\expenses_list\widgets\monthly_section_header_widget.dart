import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class MonthlySectionHeaderWidget extends StatelessWidget {
  final String monthYear;
  final double totalAmount;
  final String currency;
  final int expenseCount;

  const MonthlySectionHeaderWidget({
    Key? key,
    required this.monthYear,
    required this.totalAmount,
    this.currency = '\$',
    required this.expenseCount,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bool isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: isDark
            ? AppTheme.primaryDark.withValues(alpha: 0.1)
            : AppTheme.primaryLight.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDark
              ? AppTheme.primaryDark.withValues(alpha: 0.3)
              : AppTheme.primaryLight.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 12.w,
            height: 12.w,
            decoration: BoxDecoration(
              color: isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: CustomIconWidget(
                iconName: 'calendar_month',
                color: Colors.white,
                size: 6.w,
              ),
            ),
          ),
          SizedBox(width: 4.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  monthYear,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: isDark
                            ? AppTheme.primaryDark
                            : AppTheme.primaryLight,
                      ),
                ),
                SizedBox(height: 0.5.h),
                Text(
                  '$expenseCount expense${expenseCount != 1 ? 's' : ''}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: isDark
                            ? AppTheme.onSurfaceVariantDark
                            : AppTheme.onSurfaceVariantLight,
                      ),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                'Total',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: isDark
                          ? AppTheme.onSurfaceVariantDark
                          : AppTheme.onSurfaceVariantLight,
                    ),
              ),
              SizedBox(height: 0.5.h),
              Text(
                '$currency${totalAmount.toStringAsFixed(2)}',
                style: AppTheme.getCurrencyStyle(
                  isLight: !isDark,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
