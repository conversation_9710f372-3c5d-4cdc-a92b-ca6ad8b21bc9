import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class AdvancedOptionsWidget extends StatefulWidget {
  final Function(Map<String, dynamic>) onOptionsChanged;
  final Map<String, dynamic> optionsData;

  const AdvancedOptionsWidget({
    Key? key,
    required this.onOptionsChanged,
    required this.optionsData,
  }) : super(key: key);

  @override
  State<AdvancedOptionsWidget> createState() => _AdvancedOptionsWidgetState();
}

class _AdvancedOptionsWidgetState extends State<AdvancedOptionsWidget> {
  bool _isExpanded = false;
  final TextEditingController _tagsController = TextEditingController();
  bool _isRecurring = false;
  String _recurringFrequency = 'monthly';
  DateTime? _recurringEndDate;
  List<String> _tags = [];

  final List<String> _predefinedTags = [
    'Business',
    'Personal',
    'Travel',
    'Food',
    'Entertainment',
    'Utilities',
    'Transport',
    'Healthcare',
    'Education',
    'Shopping'
  ];

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    _tags = List<String>.from(widget.optionsData['tags'] ?? []);
    _isRecurring = widget.optionsData['isRecurring'] ?? false;
    _recurringFrequency = widget.optionsData['recurringFrequency'] ?? 'monthly';
    _recurringEndDate = widget.optionsData['recurringEndDate'];
  }

  void _updateOptionsData() {
    final updatedData = {
      ...widget.optionsData,
      'tags': _tags,
      'isRecurring': _isRecurring,
      'recurringFrequency': _recurringFrequency,
      'recurringEndDate': _recurringEndDate,
    };
    widget.onOptionsChanged(updatedData);
  }

  void _addTag(String tag) {
    if (tag.isNotEmpty && !_tags.contains(tag)) {
      setState(() {
        _tags.add(tag);
      });
      _tagsController.clear();
      _updateOptionsData();
    }
  }

  void _removeTag(String tag) {
    setState(() {
      _tags.remove(tag);
    });
    _updateOptionsData();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: () => setState(() => _isExpanded = !_isExpanded),
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
            decoration: BoxDecoration(
              color: isDark
                  ? AppTheme.surfaceVariantDark
                  : AppTheme.surfaceVariantLight,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isDark ? AppTheme.outlineDark : AppTheme.outlineLight,
              ),
            ),
            child: Row(
              children: [
                CustomIconWidget(
                  iconName: 'tune',
                  color: isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
                  size: 20,
                ),
                SizedBox(width: 3.w),
                Expanded(
                  child: Text(
                    'Advanced Options',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                ),
                CustomIconWidget(
                  iconName:
                      _isExpanded ? 'keyboard_arrow_up' : 'keyboard_arrow_down',
                  color: isDark
                      ? AppTheme.onSurfaceVariantDark
                      : AppTheme.onSurfaceVariantLight,
                  size: 20,
                ),
              ],
            ),
          ),
        ),
        if (_isExpanded) ...[
          SizedBox(height: 2.h),

          // Tags Section
          Text(
            'Tags',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
          SizedBox(height: 1.h),

          // Tag Input
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _tagsController,
                  decoration: InputDecoration(
                    hintText: 'Add a tag...',
                    prefixIcon: Padding(
                      padding: EdgeInsets.all(3.w),
                      child: CustomIconWidget(
                        iconName: 'local_offer',
                        color: isDark
                            ? AppTheme.primaryDark
                            : AppTheme.primaryLight,
                        size: 20,
                      ),
                    ),
                  ),
                  onFieldSubmitted: _addTag,
                ),
              ),
              SizedBox(width: 2.w),
              ElevatedButton(
                onPressed: () => _addTag(_tagsController.text),
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
                ),
                child: Text('Add'),
              ),
            ],
          ),

          // Predefined Tags
          if (_tagsController.text.isEmpty) ...[
            SizedBox(height: 1.h),
            Text(
              'Suggested Tags:',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
            ),
            SizedBox(height: 0.5.h),
            Wrap(
              spacing: 2.w,
              runSpacing: 1.h,
              children: _predefinedTags.map((tag) {
                return ActionChip(
                  label: Text(
                    tag,
                    style: TextStyle(
                      fontSize: 11.sp,
                      color: isDark
                          ? AppTheme.onSurfaceDark
                          : AppTheme.onSurfaceLight,
                    ),
                  ),
                  backgroundColor: isDark
                      ? AppTheme.surfaceVariantDark
                      : AppTheme.surfaceVariantLight,
                  onPressed: () => _addTag(tag),
                );
              }).toList(),
            ),
          ],

          // Current Tags
          if (_tags.isNotEmpty) ...[
            SizedBox(height: 1.h),
            Text(
              'Current Tags:',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
            ),
            SizedBox(height: 0.5.h),
            Wrap(
              spacing: 2.w,
              runSpacing: 1.h,
              children: _tags.map((tag) {
                return Chip(
                  label: Text(
                    tag,
                    style: TextStyle(
                      fontSize: 11.sp,
                      color: isDark
                          ? AppTheme.onPrimaryDark
                          : AppTheme.onPrimaryLight,
                    ),
                  ),
                  backgroundColor:
                      isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
                  deleteIcon: CustomIconWidget(
                    iconName: 'close',
                    color: isDark
                        ? AppTheme.onPrimaryDark
                        : AppTheme.onPrimaryLight,
                    size: 16,
                  ),
                  onDeleted: () => _removeTag(tag),
                );
              }).toList(),
            ),
          ],

          SizedBox(height: 3.h),

          // Recurring Expense Section
          Text(
            'Recurring Expense',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
          SizedBox(height: 1.h),

          SwitchListTile(
            title: Text(
              'Make this a recurring expense',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            subtitle: Text(
              'Automatically create this expense at regular intervals',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            value: _isRecurring,
            onChanged: (value) {
              setState(() => _isRecurring = value);
              _updateOptionsData();
            },
            contentPadding: EdgeInsets.zero,
          ),

          if (_isRecurring) ...[
            SizedBox(height: 2.h),

            // Frequency Selection
            Text(
              'Frequency',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
            ),
            SizedBox(height: 1.h),

            DropdownButtonFormField<String>(
              value: _recurringFrequency,
              decoration: InputDecoration(
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
              ),
              items: [
                DropdownMenuItem(value: 'daily', child: Text('Daily')),
                DropdownMenuItem(value: 'weekly', child: Text('Weekly')),
                DropdownMenuItem(value: 'monthly', child: Text('Monthly')),
                DropdownMenuItem(value: 'yearly', child: Text('Yearly')),
              ],
              onChanged: (value) {
                if (value != null) {
                  setState(() => _recurringFrequency = value);
                  _updateOptionsData();
                }
              },
            ),

            SizedBox(height: 2.h),

            // End Date Selection
            Text(
              'End Date (Optional)',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
            ),
            SizedBox(height: 1.h),

            GestureDetector(
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: _recurringEndDate ??
                      DateTime.now().add(Duration(days: 365)),
                  firstDate: DateTime.now(),
                  lastDate: DateTime.now().add(Duration(days: 3650)),
                );
                if (date != null) {
                  setState(() => _recurringEndDate = date);
                  _updateOptionsData();
                }
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
                decoration: BoxDecoration(
                  border: Border.all(
                    color:
                        isDark ? AppTheme.outlineDark : AppTheme.outlineLight,
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    CustomIconWidget(
                      iconName: 'event',
                      color:
                          isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
                      size: 20,
                    ),
                    SizedBox(width: 3.w),
                    Expanded(
                      child: Text(
                        _recurringEndDate != null
                            ? '${_recurringEndDate!.day}/${_recurringEndDate!.month}/${_recurringEndDate!.year}'
                            : 'Select end date (optional)',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              color: _recurringEndDate != null
                                  ? null
                                  : (isDark
                                      ? AppTheme.onSurfaceVariantDark
                                      : AppTheme.onSurfaceVariantLight),
                            ),
                      ),
                    ),
                    if (_recurringEndDate != null)
                      GestureDetector(
                        onTap: () {
                          setState(() => _recurringEndDate = null);
                          _updateOptionsData();
                        },
                        child: CustomIconWidget(
                          iconName: 'clear',
                          color: isDark
                              ? AppTheme.onSurfaceVariantDark
                              : AppTheme.onSurfaceVariantLight,
                          size: 20,
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ],
    );
  }

  @override
  void dispose() {
    _tagsController.dispose();
    super.dispose();
  }
}
