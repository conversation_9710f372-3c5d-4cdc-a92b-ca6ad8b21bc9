import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class ActivityItemWidget extends StatelessWidget {
  final Map<String, dynamic> activity;

  const ActivityItemWidget({
    Key? key,
    required this.activity,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final type = activity["type"] as String;
    final user = activity["user"] as String;
    final description = activity["description"] as String;
    final timestamp = activity["timestamp"] as DateTime;
    final avatar = activity["avatar"] as String;
    final amount = activity["amount"] as double?;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 10.w,
            height: 10.w,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: AppTheme.lightTheme.colorScheme.outline,
                width: 1,
              ),
            ),
            child: ClipOval(
              child: CustomImageWidget(
                imageUrl: avatar,
                width: 10.w,
                height: 10.w,
                fit: BoxFit.cover,
              ),
            ),
          ),
          SizedBox(width: 3.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(
                          horizontal: 2.w, vertical: 0.5.h),
                      decoration: BoxDecoration(
                        color: _getActivityColor(type).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          CustomIconWidget(
                            iconName: _getActivityIcon(type),
                            color: _getActivityColor(type),
                            size: 4.w,
                          ),
                          SizedBox(width: 1.w),
                          Text(
                            _getActivityTypeText(type),
                            style: AppTheme.lightTheme.textTheme.bodySmall
                                ?.copyWith(
                              color: _getActivityColor(type),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Spacer(),
                    Text(
                      _formatTimestamp(timestamp),
                      style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                        color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 1.h),
                RichText(
                  text: TextSpan(
                    style: AppTheme.lightTheme.textTheme.bodyMedium,
                    children: [
                      TextSpan(
                        text: user,
                        style: TextStyle(fontWeight: FontWeight.w600),
                      ),
                      TextSpan(text: " $description"),
                      if (amount != null)
                        TextSpan(
                          text: " \$${amount.toStringAsFixed(2)}",
                          style: AppTheme.getCurrencyStyle(
                            isLight: true,
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w600,
                            color: AppTheme.lightTheme.colorScheme.primary,
                          ),
                        ),
                    ],
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getActivityIcon(String type) {
    switch (type.toLowerCase()) {
      case 'expense_added':
        return 'add_circle';
      case 'expense_edited':
        return 'edit';
      case 'expense_deleted':
        return 'delete';
      case 'payment_made':
        return 'payment';
      case 'member_added':
        return 'person_add';
      case 'member_removed':
        return 'person_remove';
      case 'group_created':
        return 'group_add';
      case 'settlement':
        return 'account_balance';
      default:
        return 'info';
    }
  }

  Color _getActivityColor(String type) {
    switch (type.toLowerCase()) {
      case 'expense_added':
        return AppTheme.lightTheme.colorScheme.primary;
      case 'expense_edited':
        return Colors.orange;
      case 'expense_deleted':
        return AppTheme.lightTheme.colorScheme.error;
      case 'payment_made':
      case 'settlement':
        return AppTheme.lightTheme.colorScheme.tertiary;
      case 'member_added':
        return Colors.blue;
      case 'member_removed':
        return Colors.red;
      case 'group_created':
        return Colors.purple;
      default:
        return AppTheme.lightTheme.colorScheme.onSurfaceVariant;
    }
  }

  String _getActivityTypeText(String type) {
    switch (type.toLowerCase()) {
      case 'expense_added':
        return 'Added';
      case 'expense_edited':
        return 'Edited';
      case 'expense_deleted':
        return 'Deleted';
      case 'payment_made':
        return 'Payment';
      case 'member_added':
        return 'Joined';
      case 'member_removed':
        return 'Left';
      case 'group_created':
        return 'Created';
      case 'settlement':
        return 'Settled';
      default:
        return 'Activity';
    }
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return "Just now";
    } else if (difference.inMinutes < 60) {
      return "${difference.inMinutes}m ago";
    } else if (difference.inHours < 24) {
      return "${difference.inHours}h ago";
    } else if (difference.inDays < 7) {
      return "${difference.inDays}d ago";
    } else {
      return "${timestamp.day}/${timestamp.month}";
    }
  }
}
