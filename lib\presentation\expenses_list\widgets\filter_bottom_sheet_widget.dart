import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class FilterBottomSheetWidget extends StatefulWidget {
  final Map<String, dynamic> currentFilters;
  final Function(Map<String, dynamic>) onFiltersApplied;

  const FilterBottomSheetWidget({
    Key? key,
    required this.currentFilters,
    required this.onFiltersApplied,
  }) : super(key: key);

  @override
  State<FilterBottomSheetWidget> createState() =>
      _FilterBottomSheetWidgetState();
}

class _FilterBottomSheetWidgetState extends State<FilterBottomSheetWidget> {
  late Map<String, dynamic> _filters;
  DateTimeRange? _dateRange;
  RangeValues _amountRange = const RangeValues(0, 1000);
  List<String> _selectedGroups = [];
  List<String> _selectedCategories = [];
  String _paymentStatus = 'all';

  final List<String> _availableGroups = [
    'Trip to Paris',
    'Apartment Rent',
    'Weekend Getaway',
    'Office Lunch',
    'Birthday Party',
  ];

  final List<String> _availableCategories = [
    'Food',
    'Transport',
    'Accommodation',
    'Entertainment',
    'Shopping',
    'Utilities',
    'Health',
  ];

  @override
  void initState() {
    super.initState();
    _filters = Map<String, dynamic>.from(widget.currentFilters);
    _initializeFilters();
  }

  void _initializeFilters() {
    _dateRange = _filters['dateRange'] as DateTimeRange?;
    _amountRange =
        _filters['amountRange'] as RangeValues? ?? const RangeValues(0, 1000);
    _selectedGroups = List<String>.from(_filters['groups'] ?? []);
    _selectedCategories = List<String>.from(_filters['categories'] ?? []);
    _paymentStatus = _filters['paymentStatus'] ?? 'all';
  }

  void _applyFilters() {
    final filters = {
      'dateRange': _dateRange,
      'amountRange': _amountRange,
      'groups': _selectedGroups,
      'categories': _selectedCategories,
      'paymentStatus': _paymentStatus,
    };
    widget.onFiltersApplied(filters);
    Navigator.pop(context);
  }

  void _clearFilters() {
    setState(() {
      _dateRange = null;
      _amountRange = const RangeValues(0, 1000);
      _selectedGroups.clear();
      _selectedCategories.clear();
      _paymentStatus = 'all';
    });
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _dateRange,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            datePickerTheme: DatePickerTheme.defaults(context).copyWith(
              backgroundColor: Theme.of(context).colorScheme.surface,
              headerBackgroundColor: Theme.of(context).colorScheme.primary,
              headerForegroundColor: Theme.of(context).colorScheme.onPrimary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _dateRange = picked;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final bool isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      height: 85.h,
      decoration: BoxDecoration(
        color: isDark ? AppTheme.surfaceDark : AppTheme.surfaceLight,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 12.w,
            height: 0.5.h,
            margin: EdgeInsets.only(top: 2.h),
            decoration: BoxDecoration(
              color: isDark
                  ? AppTheme.onSurfaceVariantDark
                  : AppTheme.onSurfaceVariantLight,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: EdgeInsets.all(4.w),
            child: Row(
              children: [
                Text(
                  'Filter Expenses',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: _clearFilters,
                  child: Text(
                    'Clear All',
                    style: TextStyle(
                      color:
                          isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),

          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 4.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Date Range
                  _buildSectionTitle('Date Range'),
                  SizedBox(height: 2.h),
                  GestureDetector(
                    onTap: _selectDateRange,
                    child: Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(4.w),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: isDark
                              ? AppTheme.outlineDark
                              : AppTheme.outlineLight,
                        ),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        children: [
                          CustomIconWidget(
                            iconName: 'date_range',
                            color: isDark
                                ? AppTheme.primaryDark
                                : AppTheme.primaryLight,
                            size: 5.w,
                          ),
                          SizedBox(width: 3.w),
                          Expanded(
                            child: Text(
                              _dateRange != null
                                  ? '${_dateRange!.start.day}/${_dateRange!.start.month}/${_dateRange!.start.year} - ${_dateRange!.end.day}/${_dateRange!.end.month}/${_dateRange!.end.year}'
                                  : 'Select date range',
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                          ),
                          CustomIconWidget(
                            iconName: 'arrow_forward_ios',
                            color: isDark
                                ? AppTheme.onSurfaceVariantDark
                                : AppTheme.onSurfaceVariantLight,
                            size: 4.w,
                          ),
                        ],
                      ),
                    ),
                  ),

                  SizedBox(height: 4.h),

                  // Amount Range
                  _buildSectionTitle('Amount Range'),
                  SizedBox(height: 2.h),
                  Container(
                    padding: EdgeInsets.all(4.w),
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: isDark
                            ? AppTheme.outlineDark
                            : AppTheme.outlineLight,
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              '\$${_amountRange.start.toInt()}',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    fontWeight: FontWeight.w500,
                                  ),
                            ),
                            Text(
                              '\$${_amountRange.end.toInt()}',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    fontWeight: FontWeight.w500,
                                  ),
                            ),
                          ],
                        ),
                        RangeSlider(
                          values: _amountRange,
                          min: 0,
                          max: 1000,
                          divisions: 100,
                          onChanged: (RangeValues values) {
                            setState(() {
                              _amountRange = values;
                            });
                          },
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: 4.h),

                  // Groups
                  _buildSectionTitle('Groups'),
                  SizedBox(height: 2.h),
                  Wrap(
                    spacing: 2.w,
                    runSpacing: 1.h,
                    children: _availableGroups.map((group) {
                      final isSelected = _selectedGroups.contains(group);
                      return FilterChip(
                        label: Text(group),
                        selected: isSelected,
                        onSelected: (selected) {
                          setState(() {
                            if (selected) {
                              _selectedGroups.add(group);
                            } else {
                              _selectedGroups.remove(group);
                            }
                          });
                        },
                        backgroundColor: isDark
                            ? AppTheme.surfaceVariantDark
                            : AppTheme.surfaceVariantLight,
                        selectedColor: (isDark
                                ? AppTheme.primaryDark
                                : AppTheme.primaryLight)
                            .withValues(alpha: 0.2),
                        checkmarkColor: isDark
                            ? AppTheme.primaryDark
                            : AppTheme.primaryLight,
                        labelStyle: TextStyle(
                          color: isSelected
                              ? (isDark
                                  ? AppTheme.primaryDark
                                  : AppTheme.primaryLight)
                              : (isDark
                                  ? AppTheme.onSurfaceDark
                                  : AppTheme.onSurfaceLight),
                        ),
                      );
                    }).toList(),
                  ),

                  SizedBox(height: 4.h),

                  // Categories
                  _buildSectionTitle('Categories'),
                  SizedBox(height: 2.h),
                  Wrap(
                    spacing: 2.w,
                    runSpacing: 1.h,
                    children: _availableCategories.map((category) {
                      final isSelected = _selectedCategories.contains(category);
                      return FilterChip(
                        label: Text(category),
                        selected: isSelected,
                        onSelected: (selected) {
                          setState(() {
                            if (selected) {
                              _selectedCategories.add(category);
                            } else {
                              _selectedCategories.remove(category);
                            }
                          });
                        },
                        backgroundColor: isDark
                            ? AppTheme.surfaceVariantDark
                            : AppTheme.surfaceVariantLight,
                        selectedColor: (isDark
                                ? AppTheme.primaryDark
                                : AppTheme.primaryLight)
                            .withValues(alpha: 0.2),
                        checkmarkColor: isDark
                            ? AppTheme.primaryDark
                            : AppTheme.primaryLight,
                        labelStyle: TextStyle(
                          color: isSelected
                              ? (isDark
                                  ? AppTheme.primaryDark
                                  : AppTheme.primaryLight)
                              : (isDark
                                  ? AppTheme.onSurfaceDark
                                  : AppTheme.onSurfaceLight),
                        ),
                      );
                    }).toList(),
                  ),

                  SizedBox(height: 4.h),

                  // Payment Status
                  _buildSectionTitle('Payment Status'),
                  SizedBox(height: 2.h),
                  Column(
                    children: [
                      RadioListTile<String>(
                        title: const Text('All'),
                        value: 'all',
                        groupValue: _paymentStatus,
                        onChanged: (value) {
                          setState(() {
                            _paymentStatus = value!;
                          });
                        },
                      ),
                      RadioListTile<String>(
                        title: const Text('Paid'),
                        value: 'paid',
                        groupValue: _paymentStatus,
                        onChanged: (value) {
                          setState(() {
                            _paymentStatus = value!;
                          });
                        },
                      ),
                      RadioListTile<String>(
                        title: const Text('Unpaid'),
                        value: 'unpaid',
                        groupValue: _paymentStatus,
                        onChanged: (value) {
                          setState(() {
                            _paymentStatus = value!;
                          });
                        },
                      ),
                    ],
                  ),

                  SizedBox(height: 4.h),
                ],
              ),
            ),
          ),

          // Apply Button
          Container(
            padding: EdgeInsets.all(4.w),
            child: SizedBox(
              width: double.infinity,
              height: 6.h,
              child: ElevatedButton(
                onPressed: _applyFilters,
                child: Text(
                  'Apply Filters',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
    );
  }
}
