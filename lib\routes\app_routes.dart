import 'package:flutter/material.dart';
import '../presentation/group_details/group_details.dart';
import '../presentation/groups_list/groups_list.dart';
import '../presentation/home_dashboard/home_dashboard.dart';
import '../presentation/expenses_list/expenses_list.dart';
import '../presentation/add_expense/add_expense.dart';
import '../presentation/settlement_screen/settlement_screen.dart';

class AppRoutes {
  // TODO: Add your routes here
  static const String initial = '/';
  static const String groupDetails = '/group-details';
  static const String groupsList = '/groups-list';
  static const String homeDashboard = '/home-dashboard';
  static const String expensesList = '/expenses-list';
  static const String addExpense = '/add-expense';
  static const String settlementScreen = '/settlement-screen';

  static Map<String, WidgetBuilder> routes = {
    initial: (context) => HomeDashboard(),
    groupDetails: (context) => GroupDetails(),
    groupsList: (context) => GroupsList(),
    homeDashboard: (context) => HomeDashboard(),
    expensesList: (context) => ExpensesList(),
    addExpense: (context) => AddExpense(),
    settlementScreen: (context) => SettlementScreen(),
    // TODO: Add your other routes here
  };
}
