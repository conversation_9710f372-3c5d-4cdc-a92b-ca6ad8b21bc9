import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class GroupCardWidget extends StatelessWidget {
  final Map<String, dynamic> groupData;
  final VoidCallback onTap;
  final VoidCallback? onAddExpense;
  final VoidCallback? onViewSummary;
  final VoidCallback? onShareGroup;
  final VoidCallback? onEditGroup;
  final VoidCallback? onArchiveGroup;
  final VoidCallback? onLeaveGroup;

  const GroupCardWidget({
    Key? key,
    required this.groupData,
    required this.onTap,
    this.onAddExpense,
    this.onViewSummary,
    this.onShareGroup,
    this.onEditGroup,
    this.onArchiveGroup,
    this.onLeaveGroup,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bool isDark = Theme.of(context).brightness == Brightness.dark;
    final members = (groupData['members'] as List? ?? []);
    final visibleMembers = members.take(4).toList();
    final remainingCount = members.length > 4 ? members.length - 4 : 0;
    final userBalance = groupData['userBalance'] as double? ?? 0.0;
    final totalExpenses = groupData['totalExpenses'] as double? ?? 0.0;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: EdgeInsets.all(4.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    // Group cover image
                    Hero(
                      tag: 'group_${groupData['id']}',
                      child: Container(
                        width: 15.w,
                        height: 15.w,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          color: AppTheme
                              .lightTheme.colorScheme.surfaceContainerHighest,
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: groupData['coverImage'] != null
                              ? CustomImageWidget(
                                  imageUrl: groupData['coverImage'] as String,
                                  width: 15.w,
                                  height: 15.w,
                                  fit: BoxFit.cover,
                                )
                              : Container(
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: [
                                        AppTheme.lightTheme.primaryColor,
                                        AppTheme
                                            .lightTheme.colorScheme.secondary,
                                      ],
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    ),
                                  ),
                                  child: Center(
                                    child: Text(
                                      (groupData['name'] as String? ?? 'G')[0]
                                          .toUpperCase(),
                                      style: Theme.of(context)
                                          .textTheme
                                          .titleLarge
                                          ?.copyWith(
                                            color: Colors.white,
                                            fontWeight: FontWeight.bold,
                                          ),
                                    ),
                                  ),
                                ),
                        ),
                      ),
                    ),
                    SizedBox(width: 3.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            groupData['name'] as String? ?? 'Unnamed Group',
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          SizedBox(height: 0.5.h),
                          Row(
                            children: [
                              // Member avatars
                              ...visibleMembers
                                  .map((member) => Container(
                                        margin: EdgeInsets.only(right: 1.w),
                                        child: CircleAvatar(
                                          radius: 2.5.w,
                                          backgroundColor: AppTheme
                                              .lightTheme
                                              .colorScheme
                                              .surfaceContainerHighest,
                                          backgroundImage: member['avatar'] !=
                                                  null
                                              ? NetworkImage(
                                                  member['avatar'] as String)
                                              : null,
                                          child: member['avatar'] == null
                                              ? Text(
                                                  (member['name'] as String? ??
                                                          'U')[0]
                                                      .toUpperCase(),
                                                  style: TextStyle(
                                                    fontSize: 10.sp,
                                                    fontWeight: FontWeight.w500,
                                                    color: AppTheme
                                                        .lightTheme
                                                        .colorScheme
                                                        .onSurfaceVariant,
                                                  ),
                                                )
                                              : null,
                                        ),
                                      ))
                                  .toList(),
                              if (remainingCount > 0)
                                Container(
                                  width: 5.w,
                                  height: 5.w,
                                  decoration: BoxDecoration(
                                    color: AppTheme.lightTheme.colorScheme
                                        .surfaceContainerHighest,
                                    shape: BoxShape.circle,
                                  ),
                                  child: Center(
                                    child: Text(
                                      '+$remainingCount',
                                      style: TextStyle(
                                        fontSize: 8.sp,
                                        fontWeight: FontWeight.w500,
                                        color: AppTheme.lightTheme.colorScheme
                                            .onSurfaceVariant,
                                      ),
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          '\$${totalExpenses.toStringAsFixed(2)}',
                          style:
                              Theme.of(context).textTheme.titleSmall?.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                        ),
                        SizedBox(height: 0.5.h),
                        Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: 2.w, vertical: 0.5.h),
                          decoration: BoxDecoration(
                            color: userBalance >= 0
                                ? AppTheme
                                    .lightTheme.colorScheme.tertiaryContainer
                                : AppTheme
                                    .lightTheme.colorScheme.errorContainer,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            userBalance >= 0
                                ? '+\$${userBalance.abs().toStringAsFixed(2)}'
                                : '-\$${userBalance.abs().toStringAsFixed(2)}',
                            style: Theme.of(context)
                                .textTheme
                                .labelSmall
                                ?.copyWith(
                                  color: userBalance >= 0
                                      ? AppTheme.lightTheme.colorScheme.tertiary
                                      : AppTheme.lightTheme.colorScheme.error,
                                  fontWeight: FontWeight.w600,
                                ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                SizedBox(height: 2.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      groupData['lastActivity'] as String? ??
                          'No recent activity',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color:
                                Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                    ),
                    Row(
                      children: [
                        CustomIconWidget(
                          iconName: 'people',
                          size: 4.w,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                        SizedBox(width: 1.w),
                        Text(
                          '${members.length}',
                          style:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurfaceVariant,
                                  ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
