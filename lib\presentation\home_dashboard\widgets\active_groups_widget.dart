import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class ActiveGroupsWidget extends StatelessWidget {
  final List<Map<String, dynamic>> groups;
  final Function(Map<String, dynamic>)? onGroupTap;
  final Function(Map<String, dynamic>)? onGroupLongPress;

  const ActiveGroupsWidget({
    Key? key,
    required this.groups,
    this.onGroupTap,
    this.onGroupLongPress,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (groups.isEmpty) {
      return _buildEmptyState(context);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Active Groups',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      fontSize: 16.sp,
                    ),
              ),
              TextButton(
                onPressed: () => Navigator.pushNamed(context, '/groups-list'),
                child: Text(
                  'View All',
                  style: TextStyle(
                    color: AppTheme.lightTheme.primaryColor,
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(
          height: 25.h,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: EdgeInsets.symmetric(horizontal: 2.w),
            itemCount: groups.length,
            itemBuilder: (context, index) {
              final group = groups[index];
              return _buildGroupCard(context, group);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildGroupCard(BuildContext context, Map<String, dynamic> group) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final userBalance = (group['userBalance'] as double?) ?? 0.0;
    final isPositive = userBalance >= 0;

    return GestureDetector(
      onTap: () => onGroupTap?.call(group),
      onLongPress: () => _showGroupContextMenu(context, group),
      child: Container(
        width: 70.w,
        margin: EdgeInsets.symmetric(horizontal: 2.w),
        child: Card(
          elevation: 3,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Group Cover Image
              Container(
                height: 12.h,
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(16),
                  ),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppTheme.lightTheme.primaryColor,
                      AppTheme.secondaryLight,
                    ],
                  ),
                ),
                child: Stack(
                  children: [
                    if (group['coverImage'] != null)
                      ClipRRect(
                        borderRadius: const BorderRadius.vertical(
                          top: Radius.circular(16),
                        ),
                        child: CustomImageWidget(
                          imageUrl: group['coverImage'],
                          width: double.infinity,
                          height: 12.h,
                          fit: BoxFit.cover,
                        ),
                      ),
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: const BorderRadius.vertical(
                          top: Radius.circular(16),
                        ),
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            Colors.black.withValues(alpha: 0.3),
                          ],
                        ),
                      ),
                    ),
                    Positioned(
                      bottom: 2.w,
                      left: 3.w,
                      child: Text(
                        group['name'] ?? 'Unnamed Group',
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 14.sp,
                                ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),

              // Group Details
              Expanded(
                child: Padding(
                  padding: EdgeInsets.all(3.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Member Count and Total Expenses
                      Row(
                        children: [
                          CustomIconWidget(
                            iconName: 'group',
                            color: isDark
                                ? AppTheme.onSurfaceVariantDark
                                : AppTheme.onSurfaceVariantLight,
                            size: 16,
                          ),
                          SizedBox(width: 1.w),
                          Text(
                            '${group['memberCount'] ?? 0} members',
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: isDark
                                          ? AppTheme.onSurfaceVariantDark
                                          : AppTheme.onSurfaceVariantLight,
                                      fontSize: 11.sp,
                                    ),
                          ),
                          const Spacer(),
                          Text(
                            group['totalExpenses'] ?? '\$0.00',
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      fontWeight: FontWeight.w500,
                                      fontSize: 11.sp,
                                    ),
                          ),
                        ],
                      ),
                      SizedBox(height: 1.h),

                      // User Balance
                      Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 2.w, vertical: 1.h),
                        decoration: BoxDecoration(
                          color: (isPositive
                                  ? AppTheme.successLight
                                  : AppTheme.errorLight)
                              .withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              isPositive ? 'You are owed' : 'You owe',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall
                                  ?.copyWith(
                                    fontSize: 10.sp,
                                    color: isPositive
                                        ? AppTheme.successLight
                                        : AppTheme.errorLight,
                                  ),
                            ),
                            Text(
                              '\$${userBalance.abs().toStringAsFixed(2)}',
                              style: AppTheme.getCurrencyStyle(
                                isLight: !isDark,
                                fontSize: 12.sp,
                                fontWeight: FontWeight.w600,
                                color: isPositive
                                    ? AppTheme.successLight
                                    : AppTheme.errorLight,
                              ),
                            ),
                          ],
                        ),
                      ),

                      SizedBox(height: 1.h),

                      // Last Activity
                      if (group['lastActivity'] != null)
                        Text(
                          'Last activity: ${_formatLastActivity(group['lastActivity'])}',
                          style:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: isDark
                                        ? AppTheme.onSurfaceVariantDark
                                        : AppTheme.onSurfaceVariantLight,
                                    fontSize: 9.sp,
                                  ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
      padding: EdgeInsets.all(8.w),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          CustomIconWidget(
            iconName: 'group_add',
            color: AppTheme.lightTheme.primaryColor.withValues(alpha: 0.5),
            size: 48,
          ),
          SizedBox(height: 2.h),
          Text(
            'No Active Groups',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
          SizedBox(height: 1.h),
          Text(
            'Create your first group to start splitting expenses with friends',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? AppTheme.onSurfaceVariantDark
                      : AppTheme.onSurfaceVariantLight,
                ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 3.h),
          ElevatedButton.icon(
            onPressed: () => Navigator.pushNamed(context, '/groups-list'),
            icon: CustomIconWidget(
              iconName: 'add',
              color: Colors.white,
              size: 18,
            ),
            label: Text('Create Group'),
          ),
        ],
      ),
    );
  }

  void _showGroupContextMenu(BuildContext context, Map<String, dynamic> group) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.all(4.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 12.w,
              height: 0.5.h,
              decoration: BoxDecoration(
                color: Theme.of(context).dividerColor,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            SizedBox(height: 2.h),
            Text(
              group['name'] ?? 'Group Options',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
            SizedBox(height: 3.h),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'visibility',
                color: AppTheme.lightTheme.primaryColor,
                size: 24,
              ),
              title: Text('View Details'),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/group-details');
              },
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'add_circle',
                color: AppTheme.successLight,
                size: 24,
              ),
              title: Text('Add Expense'),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/add-expense');
              },
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'exit_to_app',
                color: AppTheme.errorLight,
                size: 24,
              ),
              title: Text('Leave Group'),
              onTap: () {
                Navigator.pop(context);
                _showLeaveGroupDialog(context, group);
              },
            ),
            SizedBox(height: 2.h),
          ],
        ),
      ),
    );
  }

  void _showLeaveGroupDialog(BuildContext context, Map<String, dynamic> group) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Leave Group'),
        content: Text(
            'Are you sure you want to leave "${group['name']}"? You won\'t be able to rejoin without an invitation.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Handle leave group logic here
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorLight,
            ),
            child: Text('Leave'),
          ),
        ],
      ),
    );
  }

  String _formatLastActivity(dynamic lastActivity) {
    if (lastActivity == null) return 'No activity';

    DateTime dateTime;
    if (lastActivity is DateTime) {
      dateTime = lastActivity;
    } else if (lastActivity is String) {
      dateTime = DateTime.tryParse(lastActivity) ?? DateTime.now();
    } else {
      return 'No activity';
    }

    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays < 1) {
      return 'Today';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${dateTime.day}/${dateTime.month}';
    }
  }
}
