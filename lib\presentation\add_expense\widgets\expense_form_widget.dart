import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class ExpenseFormWidget extends StatefulWidget {
  final Function(Map<String, dynamic>) onFormChanged;
  final Map<String, dynamic> formData;

  const ExpenseFormWidget({
    Key? key,
    required this.onFormChanged,
    required this.formData,
  }) : super(key: key);

  @override
  State<ExpenseFormWidget> createState() => _ExpenseFormWidgetState();
}

class _ExpenseFormWidgetState extends State<ExpenseFormWidget> {
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  final List<String> _expenseSuggestions = [
    'Dinner',
    'Lunch',
    'Breakfast',
    'Gas',
    'Groceries',
    'Hotel',
    'Flight',
    'Taxi',
    'Coffee',
    'Movie',
    'Shopping',
    'Parking'
  ];

  final List<Map<String, dynamic>> _categories = [
    {'name': 'Food', 'icon': 'restaurant'},
    {'name': 'Transport', 'icon': 'directions_car'},
    {'name': 'Hotel', 'icon': 'hotel'},
    {'name': 'Entertainment', 'icon': 'movie'},
    {'name': 'Shopping', 'icon': 'shopping_bag'},
    {'name': 'Other', 'icon': 'category'},
  ];

  String _selectedCategory = 'Food';
  String _selectedCurrency = 'USD';
  DateTime _selectedDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _descriptionController.text = widget.formData['description'] ?? '';
    _amountController.text = widget.formData['amount']?.toString() ?? '';
    _notesController.text = widget.formData['notes'] ?? '';
    _selectedCategory = widget.formData['category'] ?? 'Food';
    _selectedCurrency = widget.formData['currency'] ?? 'USD';
    _selectedDate = widget.formData['date'] ?? DateTime.now();
  }

  void _updateFormData() {
    final updatedData = {
      ...widget.formData,
      'description': _descriptionController.text,
      'amount': double.tryParse(_amountController.text) ?? 0.0,
      'notes': _notesController.text,
      'category': _selectedCategory,
      'currency': _selectedCurrency,
      'date': _selectedDate,
    };
    widget.onFormChanged(updatedData);
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Description Field with Suggestions
        Text(
          'Description',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        SizedBox(height: 1.h),
        TextFormField(
          controller: _descriptionController,
          decoration: InputDecoration(
            hintText: 'What was this expense for?',
            prefixIcon: Padding(
              padding: EdgeInsets.all(3.w),
              child: CustomIconWidget(
                iconName: 'description',
                color: isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
                size: 20,
              ),
            ),
          ),
          onChanged: (value) => _updateFormData(),
        ),

        // Expense Suggestions
        if (_descriptionController.text.isEmpty) ...[
          SizedBox(height: 1.h),
          SizedBox(
            height: 5.h,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _expenseSuggestions.length,
              itemBuilder: (context, index) {
                final suggestion = _expenseSuggestions[index];
                return Container(
                  margin: EdgeInsets.only(right: 2.w),
                  child: ActionChip(
                    label: Text(
                      suggestion,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: isDark
                            ? AppTheme.onSurfaceDark
                            : AppTheme.onSurfaceLight,
                      ),
                    ),
                    backgroundColor: isDark
                        ? AppTheme.surfaceVariantDark
                        : AppTheme.surfaceVariantLight,
                    onPressed: () {
                      _descriptionController.text = suggestion;
                      _updateFormData();
                    },
                  ),
                );
              },
            ),
          ),
        ],

        SizedBox(height: 3.h),

        // Amount and Currency Row
        Row(
          children: [
            Expanded(
              flex: 2,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Amount',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                  SizedBox(height: 1.h),
                  TextFormField(
                    controller: _amountController,
                    keyboardType:
                        TextInputType.numberWithOptions(decimal: true),
                    decoration: InputDecoration(
                      hintText: '0.00',
                      prefixIcon: Padding(
                        padding: EdgeInsets.all(3.w),
                        child: CustomIconWidget(
                          iconName: 'attach_money',
                          color: isDark
                              ? AppTheme.primaryDark
                              : AppTheme.primaryLight,
                          size: 20,
                        ),
                      ),
                    ),
                    onChanged: (value) => _updateFormData(),
                  ),
                ],
              ),
            ),
            SizedBox(width: 4.w),
            Expanded(
              flex: 1,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Currency',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                  SizedBox(height: 1.h),
                  DropdownButtonFormField<String>(
                    value: _selectedCurrency,
                    decoration: InputDecoration(
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 3.w, vertical: 2.h),
                    ),
                    items: ['USD', 'EUR', 'GBP', 'INR', 'CAD', 'AUD', 'JPY']
                        .map((currency) => DropdownMenuItem(
                              value: currency,
                              child: Text(currency),
                            ))
                        .toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() => _selectedCurrency = value);
                        _updateFormData();
                      }
                    },
                  ),
                ],
              ),
            ),
          ],
        ),

        SizedBox(height: 3.h),

        // Category Selection
        Text(
          'Category',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        SizedBox(height: 1.h),
        SizedBox(
          height: 8.h,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _categories.length,
            itemBuilder: (context, index) {
              final category = _categories[index];
              final isSelected = _selectedCategory == category['name'];

              return Container(
                margin: EdgeInsets.only(right: 3.w),
                child: GestureDetector(
                  onTap: () {
                    setState(() => _selectedCategory = category['name']);
                    _updateFormData();
                  },
                  child: Container(
                    width: 20.w,
                    decoration: BoxDecoration(
                      color: isSelected
                          ? (isDark
                              ? AppTheme.primaryDark
                              : AppTheme.primaryLight)
                          : (isDark
                              ? AppTheme.surfaceVariantDark
                              : AppTheme.surfaceVariantLight),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected
                            ? (isDark
                                ? AppTheme.primaryDark
                                : AppTheme.primaryLight)
                            : (isDark
                                ? AppTheme.outlineDark
                                : AppTheme.outlineLight),
                        width: 1,
                      ),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CustomIconWidget(
                          iconName: category['icon'],
                          color: isSelected
                              ? (isDark
                                  ? AppTheme.onPrimaryDark
                                  : AppTheme.onPrimaryLight)
                              : (isDark
                                  ? AppTheme.onSurfaceDark
                                  : AppTheme.onSurfaceLight),
                          size: 24,
                        ),
                        SizedBox(height: 0.5.h),
                        Text(
                          category['name'],
                          style: TextStyle(
                            fontSize: 10.sp,
                            fontWeight: FontWeight.w500,
                            color: isSelected
                                ? (isDark
                                    ? AppTheme.onPrimaryDark
                                    : AppTheme.onPrimaryLight)
                                : (isDark
                                    ? AppTheme.onSurfaceDark
                                    : AppTheme.onSurfaceLight),
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),

        SizedBox(height: 3.h),

        // Date Selection
        Text(
          'Date',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        SizedBox(height: 1.h),
        GestureDetector(
          onTap: () async {
            final date = await showDatePicker(
              context: context,
              initialDate: _selectedDate,
              firstDate: DateTime(2020),
              lastDate: DateTime.now(),
            );
            if (date != null) {
              setState(() => _selectedDate = date);
              _updateFormData();
            }
          },
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
            decoration: BoxDecoration(
              border: Border.all(
                color: isDark ? AppTheme.outlineDark : AppTheme.outlineLight,
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                CustomIconWidget(
                  iconName: 'calendar_today',
                  color: isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
                  size: 20,
                ),
                SizedBox(width: 3.w),
                Text(
                  '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
                Spacer(),
                CustomIconWidget(
                  iconName: 'arrow_drop_down',
                  color: isDark
                      ? AppTheme.onSurfaceVariantDark
                      : AppTheme.onSurfaceVariantLight,
                  size: 20,
                ),
              ],
            ),
          ),
        ),

        SizedBox(height: 3.h),

        // Notes Field
        Text(
          'Notes (Optional)',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        SizedBox(height: 1.h),
        TextFormField(
          controller: _notesController,
          maxLines: 3,
          decoration: InputDecoration(
            hintText: 'Add any additional notes...',
            prefixIcon: Padding(
              padding: EdgeInsets.all(3.w),
              child: CustomIconWidget(
                iconName: 'note',
                color: isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
                size: 20,
              ),
            ),
          ),
          onChanged: (value) => _updateFormData(),
        ),
      ],
    );
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }
}
