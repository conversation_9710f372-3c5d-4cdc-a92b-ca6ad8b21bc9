import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class RecentActivityWidget extends StatelessWidget {
  final List<Map<String, dynamic>> activities;
  final Function(Map<String, dynamic>)? onActivityTap;
  final Function(Map<String, dynamic>)? onEditActivity;
  final Function(Map<String, dynamic>)? onDeleteActivity;
  final Function(Map<String, dynamic>)? onShareActivity;

  const RecentActivityWidget({
    Key? key,
    required this.activities,
    this.onActivityTap,
    this.onEditActivity,
    this.onDeleteActivity,
    this.onShareActivity,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    if (activities.isEmpty) {
      return _buildEmptyState(context);
    }

    return Card(
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Activity',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                        fontSize: 16.sp,
                      ),
                ),
                TextButton(
                  onPressed: () =>
                      Navigator.pushNamed(context, '/expenses-list'),
                  child: Text(
                    'View All',
                    style: TextStyle(
                      color: AppTheme.lightTheme.primaryColor,
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 2.h),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: activities.length > 5 ? 5 : activities.length,
              separatorBuilder: (context, index) => SizedBox(height: 2.h),
              itemBuilder: (context, index) {
                final activity = activities[index];
                return _buildActivityItem(context, activity);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem(
      BuildContext context, Map<String, dynamic> activity) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final isExpense = activity['type'] == 'expense';
    final isSettlement = activity['type'] == 'settlement';

    return Dismissible(
      key: Key(activity['id'].toString()),
      direction: DismissDirection.endToStart,
      background: Container(
        alignment: Alignment.centerRight,
        padding: EdgeInsets.only(right: 4.w),
        decoration: BoxDecoration(
          color: AppTheme.errorLight.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            GestureDetector(
              onTap: () => onEditActivity?.call(activity),
              child: Container(
                padding: EdgeInsets.all(2.w),
                decoration: BoxDecoration(
                  color: AppTheme.warningLight,
                  shape: BoxShape.circle,
                ),
                child: CustomIconWidget(
                  iconName: 'edit',
                  color: Colors.white,
                  size: 16,
                ),
              ),
            ),
            SizedBox(width: 2.w),
            GestureDetector(
              onTap: () => onShareActivity?.call(activity),
              child: Container(
                padding: EdgeInsets.all(2.w),
                decoration: BoxDecoration(
                  color: AppTheme.lightTheme.primaryColor,
                  shape: BoxShape.circle,
                ),
                child: CustomIconWidget(
                  iconName: 'share',
                  color: Colors.white,
                  size: 16,
                ),
              ),
            ),
            SizedBox(width: 2.w),
            GestureDetector(
              onTap: () => onDeleteActivity?.call(activity),
              child: Container(
                padding: EdgeInsets.all(2.w),
                decoration: BoxDecoration(
                  color: AppTheme.errorLight,
                  shape: BoxShape.circle,
                ),
                child: CustomIconWidget(
                  iconName: 'delete',
                  color: Colors.white,
                  size: 16,
                ),
              ),
            ),
          ],
        ),
      ),
      child: GestureDetector(
        onTap: () => onActivityTap?.call(activity),
        child: Container(
          padding: EdgeInsets.all(3.w),
          decoration: BoxDecoration(
            color: isDark
                ? AppTheme.surfaceVariantDark
                : AppTheme.surfaceVariantLight,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              // Activity Icon
              Container(
                width: 10.w,
                height: 10.w,
                decoration: BoxDecoration(
                  color: _getActivityColor(activity['type'])
                      .withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: CustomIconWidget(
                  iconName: _getActivityIcon(activity['type']),
                  color: _getActivityColor(activity['type']),
                  size: 20,
                ),
              ),
              SizedBox(width: 3.w),

              // Activity Details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      activity['description'] ?? 'No description',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            fontWeight: FontWeight.w500,
                            fontSize: 14.sp,
                          ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 0.5.h),
                    Text(
                      activity['groupName'] ?? 'Personal',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: isDark
                                ? AppTheme.onSurfaceVariantDark
                                : AppTheme.onSurfaceVariantLight,
                            fontSize: 12.sp,
                          ),
                    ),
                    SizedBox(height: 0.5.h),
                    Text(
                      _formatTimestamp(activity['timestamp']),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: isDark
                                ? AppTheme.onSurfaceVariantDark
                                : AppTheme.onSurfaceVariantLight,
                            fontSize: 10.sp,
                          ),
                    ),
                  ],
                ),
              ),

              // Amount
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    activity['amount'] ?? '\$0.00',
                    style: AppTheme.getCurrencyStyle(
                      isLight: !isDark,
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                      color: isExpense
                          ? AppTheme.errorLight
                          : isSettlement
                              ? AppTheme.successLight
                              : null,
                    ),
                  ),
                  if (activity['currency'] != null &&
                      activity['currency'] != 'USD')
                    Text(
                      activity['currency'],
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontSize: 10.sp,
                            color: isDark
                                ? AppTheme.onSurfaceVariantDark
                                : AppTheme.onSurfaceVariantLight,
                          ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Card(
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      child: Padding(
        padding: EdgeInsets.all(8.w),
        child: Column(
          children: [
            CustomIconWidget(
              iconName: 'receipt_long',
              color: AppTheme.lightTheme.primaryColor.withValues(alpha: 0.5),
              size: 48,
            ),
            SizedBox(height: 2.h),
            Text(
              'No Recent Activity',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
            SizedBox(height: 1.h),
            Text(
              'Start by adding your first expense or creating a group',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? AppTheme.onSurfaceVariantDark
                        : AppTheme.onSurfaceVariantLight,
                  ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 3.h),
            ElevatedButton(
              onPressed: () => Navigator.pushNamed(context, '/add-expense'),
              child: Text('Add First Expense'),
            ),
          ],
        ),
      ),
    );
  }

  String _getActivityIcon(String type) {
    switch (type) {
      case 'expense':
        return 'receipt';
      case 'settlement':
        return 'account_balance_wallet';
      case 'group_created':
        return 'group_add';
      case 'member_added':
        return 'person_add';
      default:
        return 'receipt';
    }
  }

  Color _getActivityColor(String type) {
    switch (type) {
      case 'expense':
        return AppTheme.errorLight;
      case 'settlement':
        return AppTheme.successLight;
      case 'group_created':
        return AppTheme.lightTheme.primaryColor;
      case 'member_added':
        return AppTheme.secondaryLight;
      default:
        return AppTheme.lightTheme.primaryColor;
    }
  }

  String _formatTimestamp(dynamic timestamp) {
    if (timestamp == null) return 'Unknown time';

    DateTime dateTime;
    if (timestamp is DateTime) {
      dateTime = timestamp;
    } else if (timestamp is String) {
      dateTime = DateTime.tryParse(timestamp) ?? DateTime.now();
    } else {
      return 'Unknown time';
    }

    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }
}
