import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class GroupSelectionWidget extends StatefulWidget {
  final Function(Map<String, dynamic>?) onGroupChanged;
  final Map<String, dynamic>? selectedGroup;

  const GroupSelectionWidget({
    Key? key,
    required this.onGroupChanged,
    this.selectedGroup,
  }) : super(key: key);

  @override
  State<GroupSelectionWidget> createState() => _GroupSelectionWidgetState();
}

class _GroupSelectionWidgetState extends State<GroupSelectionWidget> {
  final TextEditingController _searchController = TextEditingController();
  List<Map<String, dynamic>> _filteredGroups = [];
  bool _showDropdown = false;

  final List<Map<String, dynamic>> _mockGroups = [
    {
      "id": 1,
      "name": "Europe Trip 2024",
      "description": "Summer vacation with friends",
      "memberCount": 6,
      "totalExpenses": 2450.75,
      "currency": "EUR",
      "image":
          "https://images.unsplash.com/photo-1467269204594-9661b134dd2b?fm=jpg&q=60&w=3000",
      "members": [
        {
          "id": 1,
          "name": "<PERSON>",
          "avatar":
              "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png"
        },
        {
          "id": 2,
          "name": "Sarah Johnson",
          "avatar":
              "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png"
        },
        {
          "id": 3,
          "name": "Mike Wilson",
          "avatar":
              "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png"
        },
        {
          "id": 4,
          "name": "Emma Davis",
          "avatar":
              "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png"
        },
        {
          "id": 5,
          "name": "Alex Brown",
          "avatar":
              "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png"
        },
        {
          "id": 6,
          "name": "Lisa Garcia",
          "avatar":
              "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png"
        },
      ]
    },
    {
      "id": 2,
      "name": "Roommate Expenses",
      "description": "Shared apartment costs",
      "memberCount": 3,
      "totalExpenses": 1250.00,
      "currency": "USD",
      "image":
          "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?fm=jpg&q=60&w=3000",
      "members": [
        {
          "id": 1,
          "name": "John Smith",
          "avatar":
              "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png"
        },
        {
          "id": 7,
          "name": "Tom Anderson",
          "avatar":
              "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png"
        },
        {
          "id": 8,
          "name": "Kate Miller",
          "avatar":
              "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png"
        },
      ]
    },
    {
      "id": 3,
      "name": "Weekend Getaway",
      "description": "Mountain cabin trip",
      "memberCount": 4,
      "totalExpenses": 890.50,
      "currency": "USD",
      "image":
          "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?fm=jpg&q=60&w=3000",
      "members": [
        {
          "id": 1,
          "name": "John Smith",
          "avatar":
              "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png"
        },
        {
          "id": 9,
          "name": "Chris Taylor",
          "avatar":
              "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png"
        },
        {
          "id": 10,
          "name": "Amy White",
          "avatar":
              "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png"
        },
        {
          "id": 11,
          "name": "David Lee",
          "avatar":
              "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png"
        },
      ]
    },
    {
      "id": 4,
      "name": "Office Lunch Group",
      "description": "Weekly team lunches",
      "memberCount": 8,
      "totalExpenses": 340.25,
      "currency": "USD",
      "image":
          "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?fm=jpg&q=60&w=3000",
      "members": [
        {
          "id": 1,
          "name": "John Smith",
          "avatar":
              "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png"
        },
        {
          "id": 12,
          "name": "Jennifer Clark",
          "avatar":
              "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png"
        },
        {
          "id": 13,
          "name": "Robert Martinez",
          "avatar":
              "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png"
        },
        {
          "id": 14,
          "name": "Michelle Rodriguez",
          "avatar":
              "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png"
        },
        {
          "id": 15,
          "name": "Kevin Thompson",
          "avatar":
              "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png"
        },
        {
          "id": 16,
          "name": "Jessica Wilson",
          "avatar":
              "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png"
        },
        {
          "id": 17,
          "name": "Daniel Moore",
          "avatar":
              "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png"
        },
        {
          "id": 18,
          "name": "Ashley Jackson",
          "avatar":
              "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png"
        },
      ]
    },
  ];

  @override
  void initState() {
    super.initState();
    _filteredGroups = _mockGroups;
  }

  void _filterGroups(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredGroups = _mockGroups;
      } else {
        _filteredGroups = _mockGroups
            .where((group) =>
                (group['name'] as String)
                    .toLowerCase()
                    .contains(query.toLowerCase()) ||
                (group['description'] as String)
                    .toLowerCase()
                    .contains(query.toLowerCase()))
            .toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select Group',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        SizedBox(height: 1.h),
        GestureDetector(
          onTap: () => setState(() => _showDropdown = !_showDropdown),
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
            decoration: BoxDecoration(
              border: Border.all(
                color: _showDropdown
                    ? (isDark ? AppTheme.primaryDark : AppTheme.primaryLight)
                    : (isDark ? AppTheme.outlineDark : AppTheme.outlineLight),
                width: _showDropdown ? 2 : 1,
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                if (widget.selectedGroup != null) ...[
                  Container(
                    width: 10.w,
                    height: 10.w,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      image: DecorationImage(
                        image: NetworkImage(widget.selectedGroup!['image']),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  SizedBox(width: 3.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.selectedGroup!['name'],
                          style:
                              Theme.of(context).textTheme.bodyLarge?.copyWith(
                                    fontWeight: FontWeight.w500,
                                  ),
                        ),
                        Text(
                          '${widget.selectedGroup!['memberCount']} members',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ],
                    ),
                  ),
                ] else ...[
                  CustomIconWidget(
                    iconName: 'group',
                    color: isDark
                        ? AppTheme.onSurfaceVariantDark
                        : AppTheme.onSurfaceVariantLight,
                    size: 20,
                  ),
                  SizedBox(width: 3.w),
                  Expanded(
                    child: Text(
                      'Choose a group for this expense',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color: isDark
                                ? AppTheme.onSurfaceVariantDark
                                : AppTheme.onSurfaceVariantLight,
                          ),
                    ),
                  ),
                ],
                CustomIconWidget(
                  iconName: _showDropdown
                      ? 'keyboard_arrow_up'
                      : 'keyboard_arrow_down',
                  color: isDark
                      ? AppTheme.onSurfaceVariantDark
                      : AppTheme.onSurfaceVariantLight,
                  size: 20,
                ),
              ],
            ),
          ),
        ),
        if (_showDropdown) ...[
          SizedBox(height: 1.h),
          Container(
            constraints: BoxConstraints(maxHeight: 40.h),
            decoration: BoxDecoration(
              color: isDark ? AppTheme.surfaceDark : AppTheme.surfaceLight,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isDark ? AppTheme.outlineDark : AppTheme.outlineLight,
              ),
              boxShadow: [
                BoxShadow(
                  color: (isDark ? AppTheme.shadowDark : AppTheme.shadowLight),
                  blurRadius: 8,
                  offset: Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                // Search Field
                Padding(
                  padding: EdgeInsets.all(3.w),
                  child: TextFormField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'Search groups...',
                      prefixIcon: Padding(
                        padding: EdgeInsets.all(3.w),
                        child: CustomIconWidget(
                          iconName: 'search',
                          color: isDark
                              ? AppTheme.onSurfaceVariantDark
                              : AppTheme.onSurfaceVariantLight,
                          size: 20,
                        ),
                      ),
                      contentPadding: EdgeInsets.symmetric(
                          horizontal: 4.w, vertical: 1.5.h),
                    ),
                    onChanged: _filterGroups,
                  ),
                ),

                // Groups List
                Expanded(
                  child: ListView.builder(
                    itemCount: _filteredGroups.length,
                    itemBuilder: (context, index) {
                      final group = _filteredGroups[index];
                      final isSelected =
                          widget.selectedGroup?['id'] == group['id'];

                      return ListTile(
                        leading: Container(
                          width: 12.w,
                          height: 12.w,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            image: DecorationImage(
                              image: NetworkImage(group['image']),
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                        title: Text(
                          group['name'],
                          style:
                              Theme.of(context).textTheme.bodyLarge?.copyWith(
                                    fontWeight: FontWeight.w500,
                                    color: isSelected
                                        ? (isDark
                                            ? AppTheme.primaryDark
                                            : AppTheme.primaryLight)
                                        : null,
                                  ),
                        ),
                        subtitle: Text(
                          '${group['memberCount']} members • ${group['currency']} \$${group['totalExpenses'].toStringAsFixed(2)}',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                        trailing: isSelected
                            ? CustomIconWidget(
                                iconName: 'check_circle',
                                color: isDark
                                    ? AppTheme.primaryDark
                                    : AppTheme.primaryLight,
                                size: 20,
                              )
                            : null,
                        onTap: () {
                          widget.onGroupChanged(group);
                          setState(() => _showDropdown = false);
                          _searchController.clear();
                          _filteredGroups = _mockGroups;
                        },
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
