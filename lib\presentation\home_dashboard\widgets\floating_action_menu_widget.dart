import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class FloatingActionMenuWidget extends StatefulWidget {
  final VoidCallback? onAddExpense;
  final VoidCallback? onCreateGroup;
  final VoidCallback? onScanReceipt;

  const FloatingActionMenuWidget({
    Key? key,
    this.onAddExpense,
    this.onCreateGroup,
    this.onScanReceipt,
  }) : super(key: key);

  @override
  State<FloatingActionMenuWidget> createState() =>
      _FloatingActionMenuWidgetState();
}

class _FloatingActionMenuWidgetState extends State<FloatingActionMenuWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleMenu() {
    setState(() {
      _isExpanded = !_isExpanded;
    });

    if (_isExpanded) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        // Backdrop
        if (_isExpanded)
          GestureDetector(
            onTap: _toggleMenu,
            child: Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.black.withValues(alpha: 0.3),
            ),
          ),

        // Menu Items
        Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            // Scan Receipt
            AnimatedBuilder(
              animation: _animation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _animation.value,
                  child: Transform.translate(
                    offset: Offset(0, (1 - _animation.value) * 20),
                    child: Opacity(
                      opacity: _animation.value,
                      child: _buildMenuItem(
                        context,
                        label: 'Scan Receipt',
                        icon: 'camera_alt',
                        color: AppTheme.warningLight,
                        onTap: () {
                          _toggleMenu();
                          widget.onScanReceipt?.call();
                        },
                      ),
                    ),
                  ),
                );
              },
            ),

            if (_isExpanded) SizedBox(height: 2.h),

            // Create Group
            AnimatedBuilder(
              animation: _animation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _animation.value,
                  child: Transform.translate(
                    offset: Offset(0, (1 - _animation.value) * 15),
                    child: Opacity(
                      opacity: _animation.value,
                      child: _buildMenuItem(
                        context,
                        label: 'Create Group',
                        icon: 'group_add',
                        color: AppTheme.secondaryLight,
                        onTap: () {
                          _toggleMenu();
                          if (widget.onCreateGroup != null) {
                            widget.onCreateGroup!();
                          } else {
                            Navigator.pushNamed(context, '/groups-list');
                          }
                        },
                      ),
                    ),
                  ),
                );
              },
            ),

            if (_isExpanded) SizedBox(height: 2.h),

            // Add Expense
            AnimatedBuilder(
              animation: _animation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _animation.value,
                  child: Transform.translate(
                    offset: Offset(0, (1 - _animation.value) * 10),
                    child: Opacity(
                      opacity: _animation.value,
                      child: _buildMenuItem(
                        context,
                        label: 'Add Expense',
                        icon: 'add_circle',
                        color: AppTheme.successLight,
                        onTap: () {
                          _toggleMenu();
                          if (widget.onAddExpense != null) {
                            widget.onAddExpense!();
                          } else {
                            Navigator.pushNamed(context, '/add-expense');
                          }
                        },
                      ),
                    ),
                  ),
                );
              },
            ),

            if (_isExpanded) SizedBox(height: 2.h),

            // Main FAB
            FloatingActionButton(
              onPressed: _toggleMenu,
              backgroundColor: AppTheme.lightTheme.primaryColor,
              child: AnimatedRotation(
                turns: _isExpanded ? 0.125 : 0,
                duration: const Duration(milliseconds: 300),
                child: CustomIconWidget(
                  iconName: _isExpanded ? 'close' : 'add',
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMenuItem(
    BuildContext context, {
    required String label,
    required String icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Label
        Container(
          padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  fontSize: 12.sp,
                ),
          ),
        ),
        SizedBox(width: 2.w),

        // Mini FAB
        GestureDetector(
          onTap: onTap,
          child: Container(
            width: 12.w,
            height: 12.w,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: color.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: CustomIconWidget(
              iconName: icon,
              color: Colors.white,
              size: 20,
            ),
          ),
        ),
      ],
    );
  }
}