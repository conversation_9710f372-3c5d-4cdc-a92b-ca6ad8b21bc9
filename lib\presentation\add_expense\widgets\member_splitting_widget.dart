import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class MemberSplittingWidget extends StatefulWidget {
  final Map<String, dynamic>? selectedGroup;
  final Function(Map<String, dynamic>) onSplitChanged;
  final Map<String, dynamic> splitData;

  const MemberSplittingWidget({
    Key? key,
    required this.selectedGroup,
    required this.onSplitChanged,
    required this.splitData,
  }) : super(key: key);

  @override
  State<MemberSplittingWidget> createState() => _MemberSplittingWidgetState();
}

class _MemberSplittingWidgetState extends State<MemberSplittingWidget> {
  String _splitType = 'equal';
  Map<int, bool> _selectedMembers = {};
  Map<int, double> _memberAmounts = {};
  Map<int, double> _memberPercentages = {};
  Map<int, int> _memberShares = {};
  int? _payerId;

  @override
  void initState() {
    super.initState();
    _initializeSplitData();
  }

  void _initializeSplitData() {
    if (widget.selectedGroup != null) {
      final members = widget.selectedGroup!['members'] as List;

      // Initialize all members as selected by default
      for (var member in members) {
        final memberId = member['id'] as int;
        _selectedMembers[memberId] =
            widget.splitData['selectedMembers']?[memberId.toString()] ?? true;
        _memberAmounts[memberId] = widget.splitData['memberAmounts']
                    ?[memberId.toString()]
                ?.toDouble() ??
            0.0;
        _memberPercentages[memberId] = widget.splitData['memberPercentages']
                    ?[memberId.toString()]
                ?.toDouble() ??
            0.0;
        _memberShares[memberId] =
            widget.splitData['memberShares']?[memberId.toString()] ?? 1;
      }

      _splitType = widget.splitData['splitType'] ?? 'equal';
      _payerId = widget.splitData['payerId'];

      if (_payerId == null && members.isNotEmpty) {
        _payerId = members.first['id'];
      }

      _updateSplitData();
    }
  }

  void _updateSplitData() {
    final updatedData = {
      'splitType': _splitType,
      'selectedMembers':
          _selectedMembers.map((key, value) => MapEntry(key.toString(), value)),
      'memberAmounts':
          _memberAmounts.map((key, value) => MapEntry(key.toString(), value)),
      'memberPercentages': _memberPercentages
          .map((key, value) => MapEntry(key.toString(), value)),
      'memberShares':
          _memberShares.map((key, value) => MapEntry(key.toString(), value)),
      'payerId': _payerId,
    };
    widget.onSplitChanged(updatedData);
  }

  void _calculateEqualSplit(double totalAmount) {
    final selectedCount =
        _selectedMembers.values.where((selected) => selected).length;
    if (selectedCount > 0) {
      final amountPerPerson = totalAmount / selectedCount;
      for (var memberId in _selectedMembers.keys) {
        if (_selectedMembers[memberId] == true) {
          _memberAmounts[memberId] = amountPerPerson;
        } else {
          _memberAmounts[memberId] = 0.0;
        }
      }
    }
  }

  void _calculatePercentageSplit(double totalAmount) {
    for (var memberId in _memberPercentages.keys) {
      if (_selectedMembers[memberId] == true) {
        _memberAmounts[memberId] =
            totalAmount * (_memberPercentages[memberId]! / 100);
      } else {
        _memberAmounts[memberId] = 0.0;
      }
    }
  }

  void _calculateSharesSplit(double totalAmount) {
    final totalShares = _memberShares.entries
        .where((entry) => _selectedMembers[entry.key] == true)
        .fold(0, (sum, entry) => sum + entry.value);

    if (totalShares > 0) {
      for (var memberId in _memberShares.keys) {
        if (_selectedMembers[memberId] == true) {
          _memberAmounts[memberId] =
              totalAmount * (_memberShares[memberId]! / totalShares);
        } else {
          _memberAmounts[memberId] = 0.0;
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    if (widget.selectedGroup == null) {
      return Container(
        padding: EdgeInsets.all(4.w),
        decoration: BoxDecoration(
          color: isDark
              ? AppTheme.surfaceVariantDark
              : AppTheme.surfaceVariantLight,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            CustomIconWidget(
              iconName: 'info',
              color: isDark
                  ? AppTheme.onSurfaceVariantDark
                  : AppTheme.onSurfaceVariantLight,
              size: 20,
            ),
            SizedBox(width: 3.w),
            Expanded(
              child: Text(
                'Please select a group first to split expenses',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: isDark
                          ? AppTheme.onSurfaceVariantDark
                          : AppTheme.onSurfaceVariantLight,
                    ),
              ),
            ),
          ],
        ),
      );
    }

    final members = widget.selectedGroup!['members'] as List;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Split Between',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        SizedBox(height: 1.h),

        // Split Type Selection
        Container(
          decoration: BoxDecoration(
            color: isDark
                ? AppTheme.surfaceVariantDark
                : AppTheme.surfaceVariantLight,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Expanded(
                child: _buildSplitTypeButton('equal', 'Equal', isDark),
              ),
              Expanded(
                child: _buildSplitTypeButton('exact', 'Exact', isDark),
              ),
              Expanded(
                child: _buildSplitTypeButton('percentage', '%', isDark),
              ),
              Expanded(
                child: _buildSplitTypeButton('shares', 'Shares', isDark),
              ),
            ],
          ),
        ),

        SizedBox(height: 2.h),

        // Members List
        Text(
          'Select Members',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        SizedBox(height: 1.h),

        ...members.map((member) {
          final memberId = member['id'] as int;
          final isSelected = _selectedMembers[memberId] ?? false;

          return Container(
            margin: EdgeInsets.only(bottom: 1.h),
            padding: EdgeInsets.all(3.w),
            decoration: BoxDecoration(
              color: isSelected
                  ? (isDark
                      ? AppTheme.primaryDark.withValues(alpha: 0.1)
                      : AppTheme.primaryLight.withValues(alpha: 0.1))
                  : (isDark ? AppTheme.surfaceDark : AppTheme.surfaceLight),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isSelected
                    ? (isDark ? AppTheme.primaryDark : AppTheme.primaryLight)
                    : (isDark ? AppTheme.outlineDark : AppTheme.outlineLight),
              ),
            ),
            child: Row(
              children: [
                Checkbox(
                  value: isSelected,
                  onChanged: (value) {
                    setState(() {
                      _selectedMembers[memberId] = value ?? false;
                    });
                    _updateSplitData();
                  },
                ),
                SizedBox(width: 2.w),
                CustomImageWidget(
                  imageUrl: member['avatar'],
                  width: 10.w,
                  height: 10.w,
                  fit: BoxFit.cover,
                ),
                SizedBox(width: 3.w),
                Expanded(
                  child: Text(
                    member['name'],
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                  ),
                ),
                if (isSelected && _splitType != 'equal') ...[
                  SizedBox(
                    width: 20.w,
                    child: _buildAmountInput(memberId, isDark),
                  ),
                ],
              ],
            ),
          );
        }).toList(),

        SizedBox(height: 2.h),

        // Who Paid Section
        Text(
          'Who Paid?',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        SizedBox(height: 1.h),

        DropdownButtonFormField<int>(
          value: _payerId,
          decoration: InputDecoration(
            contentPadding:
                EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
          ),
          items: members.map((member) {
            final memberId = member['id'] as int;
            return DropdownMenuItem<int>(
              value: memberId,
              child: Row(
                children: [
                  CustomImageWidget(
                    imageUrl: member['avatar'],
                    width: 8.w,
                    height: 8.w,
                    fit: BoxFit.cover,
                  ),
                  SizedBox(width: 3.w),
                  Text(member['name']),
                ],
              ),
            );
          }).toList(),
          onChanged: (value) {
            setState(() => _payerId = value);
            _updateSplitData();
          },
        ),
      ],
    );
  }

  Widget _buildSplitTypeButton(String type, String label, bool isDark) {
    final isSelected = _splitType == type;

    return GestureDetector(
      onTap: () {
        setState(() => _splitType = type);
        _updateSplitData();
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 1.5.h),
        decoration: BoxDecoration(
          color: isSelected
              ? (isDark ? AppTheme.primaryDark : AppTheme.primaryLight)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: Text(
            label,
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w500,
              color: isSelected
                  ? (isDark ? AppTheme.onPrimaryDark : AppTheme.onPrimaryLight)
                  : (isDark ? AppTheme.onSurfaceDark : AppTheme.onSurfaceLight),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAmountInput(int memberId, bool isDark) {
    switch (_splitType) {
      case 'exact':
        return TextFormField(
          initialValue: _memberAmounts[memberId]?.toStringAsFixed(2) ?? '0.00',
          keyboardType: TextInputType.numberWithOptions(decimal: true),
          decoration: InputDecoration(
            hintText: '0.00',
            contentPadding:
                EdgeInsets.symmetric(horizontal: 2.w, vertical: 1.h),
          ),
          onChanged: (value) {
            _memberAmounts[memberId] = double.tryParse(value) ?? 0.0;
            _updateSplitData();
          },
        );
      case 'percentage':
        return TextFormField(
          initialValue: _memberPercentages[memberId]?.toString() ?? '0',
          keyboardType: TextInputType.number,
          decoration: InputDecoration(
            hintText: '0%',
            suffixText: '%',
            contentPadding:
                EdgeInsets.symmetric(horizontal: 2.w, vertical: 1.h),
          ),
          onChanged: (value) {
            _memberPercentages[memberId] = double.tryParse(value) ?? 0.0;
            _updateSplitData();
          },
        );
      case 'shares':
        return TextFormField(
          initialValue: _memberShares[memberId]?.toString() ?? '1',
          keyboardType: TextInputType.number,
          decoration: InputDecoration(
            hintText: '1',
            contentPadding:
                EdgeInsets.symmetric(horizontal: 2.w, vertical: 1.h),
          ),
          onChanged: (value) {
            _memberShares[memberId] = int.tryParse(value) ?? 1;
            _updateSplitData();
          },
        );
      default:
        return SizedBox.shrink();
    }
  }
}
