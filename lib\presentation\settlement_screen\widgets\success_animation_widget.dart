import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../theme/app_theme.dart';

class SuccessAnimationWidget extends StatefulWidget {
  final VoidCallback onComplete;

  const SuccessAnimationWidget({
    Key? key,
    required this.onComplete,
  }) : super(key: key);

  @override
  State<SuccessAnimationWidget> createState() => _SuccessAnimationWidgetState();
}

class _SuccessAnimationWidgetState extends State<SuccessAnimationWidget>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _confettiController;
  late AnimationController _checkController;

  late Animation<double> _scaleAnimation;
  late Animation<double> _confettiAnimation;
  late Animation<double> _checkAnimation;

  @override
  void initState() {
    super.initState();

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _confettiController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _checkController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    _confettiAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _confettiController,
      curve: Curves.easeOut,
    ));

    _checkAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _checkController,
      curve: Curves.easeInOut,
    ));

    _startAnimation();
  }

  void _startAnimation() async {
    await _scaleController.forward();
    _confettiController.forward();
    await _checkController.forward();

    // Wait for animations to complete then call onComplete
    await Future.delayed(const Duration(milliseconds: 1000));
    widget.onComplete();
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _confettiController.dispose();
    _checkController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.black.withValues(alpha: 0.7),
      child: Center(
        child: AnimatedBuilder(
          animation: Listenable.merge(
              [_scaleController, _confettiController, _checkController]),
          builder: (context, child) {
            return Stack(
              alignment: Alignment.center,
              children: [
                // Confetti particles
                ..._buildConfettiParticles(),

                // Main success container
                ScaleTransition(
                  scale: _scaleAnimation,
                  child: Container(
                    width: 60.w,
                    height: 60.w,
                    decoration: BoxDecoration(
                      color: AppTheme.lightTheme.colorScheme.surface,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: AppTheme.successLight.withValues(alpha: 0.3),
                          blurRadius: 30,
                          spreadRadius: 10,
                        ),
                      ],
                    ),
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        // Success circle background
                        Container(
                          width: 45.w,
                          height: 45.w,
                          decoration: BoxDecoration(
                            color: AppTheme.successLight,
                            shape: BoxShape.circle,
                          ),
                        ),

                        // Animated check mark
                        AnimatedBuilder(
                          animation: _checkAnimation,
                          builder: (context, child) {
                            return CustomPaint(
                              size: Size(20.w, 20.w),
                              painter: CheckMarkPainter(
                                progress: _checkAnimation.value,
                                color: Colors.white,
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),

                // Success text
                Positioned(
                  bottom: 25.h,
                  child: FadeTransition(
                    opacity: _checkAnimation,
                    child: Column(
                      children: [
                        Text(
                          'Payment Confirmed!',
                          style: AppTheme.lightTheme.textTheme.headlineSmall
                              ?.copyWith(
                            fontWeight: FontWeight.w700,
                            color: Colors.white,
                          ),
                        ),
                        SizedBox(height: 1.h),
                        Text(
                          'Settlement completed successfully',
                          style: AppTheme.lightTheme.textTheme.bodyMedium
                              ?.copyWith(
                            color: Colors.white.withValues(alpha: 0.8),
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  List<Widget> _buildConfettiParticles() {
    final particles = <Widget>[];
    final colors = [
      AppTheme.lightTheme.colorScheme.primary,
      AppTheme.lightTheme.colorScheme.secondary,
      AppTheme.successLight,
      AppTheme.warningLight,
    ];

    for (int i = 0; i < 20; i++) {
      final angle = (i * 18.0) * (3.14159 / 180); // Convert to radians
      final distance = 40.w * _confettiAnimation.value;
      final x = distance * (i % 2 == 0 ? 1 : -1) * 0.5;
      final y = distance * (i % 3 == 0 ? 1 : -1) * 0.3;

      particles.add(
        Positioned(
          left: 50.w + x,
          top: 50.h + y,
          child: Transform.rotate(
            angle: angle * _confettiAnimation.value,
            child: Opacity(
              opacity: (1.0 - _confettiAnimation.value).clamp(0.0, 1.0),
              child: Container(
                width: 2.w,
                height: 2.w,
                decoration: BoxDecoration(
                  color: colors[i % colors.length],
                  shape: i % 2 == 0 ? BoxShape.circle : BoxShape.rectangle,
                  borderRadius: i % 2 == 1 ? BorderRadius.circular(1.w) : null,
                ),
              ),
            ),
          ),
        ),
      );
    }

    return particles;
  }
}

class CheckMarkPainter extends CustomPainter {
  final double progress;
  final Color color;

  CheckMarkPainter({
    required this.progress,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 4.0
      ..strokeCap = StrokeCap.round
      ..style = PaintingStyle.stroke;

    final path = Path();

    // Define check mark path
    final startX = size.width * 0.2;
    final startY = size.height * 0.5;
    final midX = size.width * 0.45;
    final midY = size.height * 0.7;
    final endX = size.width * 0.8;
    final endY = size.height * 0.3;

    if (progress <= 0.5) {
      // First part of check mark
      final currentProgress = progress * 2;
      final currentX = startX + (midX - startX) * currentProgress;
      final currentY = startY + (midY - startY) * currentProgress;

      path.moveTo(startX, startY);
      path.lineTo(currentX, currentY);
    } else {
      // Complete first part and animate second part
      final currentProgress = (progress - 0.5) * 2;
      final currentX = midX + (endX - midX) * currentProgress;
      final currentY = midY + (endY - midY) * currentProgress;

      path.moveTo(startX, startY);
      path.lineTo(midX, midY);
      path.lineTo(currentX, currentY);
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CheckMarkPainter oldDelegate) {
    return oldDelegate.progress != progress || oldDelegate.color != color;
  }
}
