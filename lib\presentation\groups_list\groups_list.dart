import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import './widgets/create_group_fab_widget.dart';
import './widgets/create_group_form_widget.dart';
import './widgets/empty_state_widget.dart';
import './widgets/filter_bottom_sheet_widget.dart';
import './widgets/group_card_widget.dart';
import './widgets/search_bar_widget.dart';

class GroupsList extends StatefulWidget {
  const GroupsList({Key? key}) : super(key: key);

  @override
  State<GroupsList> createState() => _GroupsListState();
}

class _GroupsListState extends State<GroupsList> with TickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  final RefreshIndicator _refreshIndicatorKey = RefreshIndicator(
    onRefresh: () async {},
    child: Container(),
  );

  String _searchQuery = '';
  String _selectedFilter = 'all';
  DateTimeRange? _selectedDateRange;
  bool _isLoading = false;
  bool _isMultiSelectMode = false;
  Set<int> _selectedGroups = {};

  late AnimationController _fabAnimationController;
  late Animation<Offset> _fabSlideAnimation;

  // Mock data for groups
  final List<Map<String, dynamic>> _allGroups = [
    {
      'id': 1,
      'name': 'Weekend Trip to Goa',
      'coverImage':
          'https://images.pexels.com/photos/1450360/pexels-photo-1450360.jpeg?auto=compress&cs=tinysrgb&w=800',
      'members': [
        {
          'id': 1,
          'name': 'John Doe',
          'avatar':
              'https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png'
        },
        {
          'id': 2,
          'name': 'Jane Smith',
          'avatar':
              'https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png'
        },
        {
          'id': 3,
          'name': 'Mike Johnson',
          'avatar':
              'https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png'
        },
        {
          'id': 4,
          'name': 'Sarah Wilson',
          'avatar':
              'https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png'
        },
        {
          'id': 5,
          'name': 'Tom Brown',
          'avatar':
              'https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png'
        },
      ],
      'totalExpenses': 2450.75,
      'userBalance': 125.50,
      'lastActivity': '2 hours ago',
      'status': 'active',
      'createdBy': 'me',
      'createdDate': DateTime.now().subtract(const Duration(days: 5)),
    },
    {
      'id': 2,
      'name': 'Apartment Rent & Utilities',
      'coverImage': null,
      'members': [
        {
          'id': 1,
          'name': 'Alex Chen',
          'avatar':
              'https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png'
        },
        {
          'id': 2,
          'name': 'Emma Davis',
          'avatar':
              'https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png'
        },
        {
          'id': 3,
          'name': 'Ryan Miller',
          'avatar':
              'https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png'
        },
      ],
      'totalExpenses': 3200.00,
      'userBalance': -800.00,
      'lastActivity': '1 day ago',
      'status': 'active',
      'createdBy': 'other',
      'createdDate': DateTime.now().subtract(const Duration(days: 30)),
    },
    {
      'id': 3,
      'name': 'Office Team Lunch',
      'coverImage':
          'https://images.pixabay.com/photos/2017/01/26/02/06/platter-2009590_1280.jpg',
      'members': [
        {
          'id': 1,
          'name': 'Lisa Anderson',
          'avatar':
              'https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png'
        },
        {
          'id': 2,
          'name': 'David Lee',
          'avatar':
              'https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png'
        },
        {
          'id': 3,
          'name': 'Maria Garcia',
          'avatar':
              'https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png'
        },
        {
          'id': 4,
          'name': 'James Wilson',
          'avatar':
              'https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png'
        },
      ],
      'totalExpenses': 180.25,
      'userBalance': 0.00,
      'lastActivity': '3 days ago',
      'status': 'settled',
      'createdBy': 'me',
      'createdDate': DateTime.now().subtract(const Duration(days: 10)),
    },
    {
      'id': 4,
      'name': 'Birthday Party Expenses',
      'coverImage':
          'https://images.unsplash.com/photo-1530103862676-de8c9debad1d?auto=format&fit=crop&w=800&q=80',
      'members': [
        {
          'id': 1,
          'name': 'Sophie Turner',
          'avatar':
              'https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png'
        },
        {
          'id': 2,
          'name': 'Chris Evans',
          'avatar':
              'https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png'
        },
      ],
      'totalExpenses': 450.80,
      'userBalance': 75.20,
      'lastActivity': '1 week ago',
      'status': 'active',
      'createdBy': 'other',
      'createdDate': DateTime.now().subtract(const Duration(days: 15)),
    },
  ];

  List<String> _recentSearches = ['Weekend Trip', 'Apartment', 'Office'];

  @override
  void initState() {
    super.initState();
    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fabSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 2),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _fabAnimationController,
      curve: Curves.easeOutBack,
    ));

    _scrollController.addListener(_onScroll);

    // Animate FAB on screen load
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _fabAnimationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _fabAnimationController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels > 100) {
      if (_fabAnimationController.value == 1.0) {
        _fabAnimationController.reverse();
      }
    } else {
      if (_fabAnimationController.value == 0.0) {
        _fabAnimationController.forward();
      }
    }
  }

  List<Map<String, dynamic>> get _filteredGroups {
    List<Map<String, dynamic>> filtered = _allGroups;

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered
          .where((group) => (group['name'] as String)
              .toLowerCase()
              .contains(_searchQuery.toLowerCase()))
          .toList();
    }

    // Apply status filter
    switch (_selectedFilter) {
      case 'active':
        filtered =
            filtered.where((group) => group['status'] == 'active').toList();
        break;
      case 'settled':
        filtered =
            filtered.where((group) => group['status'] == 'settled').toList();
        break;
      case 'created_by_me':
        filtered =
            filtered.where((group) => group['createdBy'] == 'me').toList();
        break;
    }

    // Apply date range filter
    if (_selectedDateRange != null) {
      filtered = filtered.where((group) {
        final DateTime createdDate = group['createdDate'] as DateTime;
        return createdDate.isAfter(_selectedDateRange!.start) &&
            createdDate
                .isBefore(_selectedDateRange!.end.add(const Duration(days: 1)));
      }).toList();
    }

    return filtered;
  }

  Future<void> _onRefresh() async {
    setState(() {
      _isLoading = true;
    });

    // Simulate API call
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _isLoading = false;
    });
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });

    // Add to recent searches if not empty and not already present
    if (query.isNotEmpty && !_recentSearches.contains(query)) {
      setState(() {
        _recentSearches.insert(0, query);
        if (_recentSearches.length > 5) {
          _recentSearches.removeLast();
        }
      });
    }
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => FilterBottomSheetWidget(
        selectedFilter: _selectedFilter,
        selectedDateRange: _selectedDateRange,
        onFilterChanged: (filter) {
          setState(() {
            _selectedFilter = filter;
          });
        },
        onDateRangeChanged: (dateRange) {
          setState(() {
            _selectedDateRange = dateRange;
          });
        },
      ),
    );
  }

  void _onGroupTap(Map<String, dynamic> group) {
    if (_isMultiSelectMode) {
      setState(() {
        if (_selectedGroups.contains(group['id'] as int)) {
          _selectedGroups.remove(group['id'] as int);
        } else {
          _selectedGroups.add(group['id'] as int);
        }

        if (_selectedGroups.isEmpty) {
          _isMultiSelectMode = false;
        }
      });
    } else {
      Navigator.pushNamed(context, '/group-details', arguments: group);
    }
  }

  void _onGroupLongPress(Map<String, dynamic> group) {
    setState(() {
      _isMultiSelectMode = true;
      _selectedGroups.add(group['id'] as int);
    });
  }

  void _exitMultiSelectMode() {
    setState(() {
      _isMultiSelectMode = false;
      _selectedGroups.clear();
    });
  }

  void _onCreateGroup() {
    // Navigate to create group screen or show modal
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CreateGroupFormWidget(
        onGroupCreated: () {
          // Refresh groups list
          setState(() {
            // Add new group to mock data or refresh from API
          });
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final filteredGroups = _filteredGroups;

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: _isMultiSelectMode
          ? AppBar(
              leading: IconButton(
                onPressed: _exitMultiSelectMode,
                icon: CustomIconWidget(
                  iconName: 'close',
                  size: 6.w,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              title: Text('${_selectedGroups.length} selected'),
              actions: [
                IconButton(
                  onPressed: () {
                    // Handle batch operations
                  },
                  icon: CustomIconWidget(
                    iconName: 'more_vert',
                    size: 6.w,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            )
          : AppBar(
              title: Text(
                'Groups',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.lightTheme.primaryColor,
                    ),
              ),
              actions: [
                if (filteredGroups.isNotEmpty)
                  IconButton(
                    onPressed: () {
                      // Navigate to notifications or settings
                    },
                    icon: CustomIconWidget(
                      iconName: 'notifications_outlined',
                      size: 6.w,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
              ],
            ),
      body: Column(
        children: [
          // Search bar
          if (!_isMultiSelectMode)
            SearchBarWidget(
              onSearchChanged: _onSearchChanged,
              onFilterTap: _showFilterBottomSheet,
              recentSearches: _recentSearches,
            ),

          // Active filters indicator
          if (_selectedFilter != 'all' || _selectedDateRange != null)
            Container(
              margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
              child: Row(
                children: [
                  if (_selectedFilter != 'all')
                    Container(
                      margin: EdgeInsets.only(right: 2.w),
                      padding:
                          EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
                      decoration: BoxDecoration(
                        color: AppTheme.lightTheme.primaryColor
                            .withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: AppTheme.lightTheme.primaryColor,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            _selectedFilter.replaceAll('_', ' ').toUpperCase(),
                            style: TextStyle(
                              color: AppTheme.lightTheme.primaryColor,
                              fontSize: 10.sp,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          SizedBox(width: 1.w),
                          GestureDetector(
                            onTap: () {
                              setState(() {
                                _selectedFilter = 'all';
                              });
                            },
                            child: CustomIconWidget(
                              iconName: 'close',
                              size: 3.w,
                              color: AppTheme.lightTheme.primaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  if (_selectedDateRange != null)
                    Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
                      decoration: BoxDecoration(
                        color: AppTheme.lightTheme.primaryColor
                            .withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: AppTheme.lightTheme.primaryColor,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'DATE RANGE',
                            style: TextStyle(
                              color: AppTheme.lightTheme.primaryColor,
                              fontSize: 10.sp,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          SizedBox(width: 1.w),
                          GestureDetector(
                            onTap: () {
                              setState(() {
                                _selectedDateRange = null;
                              });
                            },
                            child: CustomIconWidget(
                              iconName: 'close',
                              size: 3.w,
                              color: AppTheme.lightTheme.primaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),

          // Groups list
          Expanded(
            child: filteredGroups.isEmpty
                ? EmptyStateWidget(onCreateGroup: _onCreateGroup)
                : RefreshIndicator(
                    onRefresh: _onRefresh,
                    color: AppTheme.lightTheme.primaryColor,
                    child: ListView.builder(
                      controller: _scrollController,
                      physics: const AlwaysScrollableScrollPhysics(),
                      itemCount: filteredGroups.length,
                      itemBuilder: (context, index) {
                        final group = filteredGroups[index];
                        final isSelected =
                            _selectedGroups.contains(group['id'] as int);

                        return AnimatedContainer(
                          duration: const Duration(milliseconds: 200),
                          transform: Matrix4.identity()
                            ..scale(isSelected ? 0.95 : 1.0),
                          child: Container(
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? AppTheme.lightTheme.primaryColor
                                      .withValues(alpha: 0.1)
                                  : Colors.transparent,
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: GroupCardWidget(
                              groupData: group,
                              onTap: () => _onGroupTap(group),
                              onAddExpense: () {
                                Navigator.pushNamed(context, '/add-expense',
                                    arguments: group);
                              },
                              onViewSummary: () {
                                // Navigate to group summary
                              },
                              onShareGroup: () {
                                // Share group functionality
                              },
                              onEditGroup: () {
                                // Navigate to edit group
                              },
                              onArchiveGroup: () {
                                // Archive group functionality
                              },
                              onLeaveGroup: () {
                                // Leave group functionality
                              },
                            ),
                          ),
                        );
                      },
                    ),
                  ),
          ),
        ],
      ),
      floatingActionButton: filteredGroups.isNotEmpty && !_isMultiSelectMode
          ? SlideTransition(
              position: _fabSlideAnimation,
              child: CreateGroupFabWidget(
                onPressed: _onCreateGroup,
              ),
            )
          : null,
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
    );
  }
}
