import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class BalanceItemWidget extends StatelessWidget {
  final Map<String, dynamic> balanceData;
  final VoidCallback onSettleUp;

  const BalanceItemWidget({
    Key? key,
    required this.balanceData,
    required this.onSettleUp,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final fromUser = balanceData["fromUser"] as String;
    final toUser = balanceData["toUser"] as String;
    final amount = balanceData["amount"] as double;
    final fromAvatar = balanceData["fromAvatar"] as String;
    final toAvatar = balanceData["toAvatar"] as String;

    return Card(
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Row(
          children: [
            // From user avatar
            Container(
              width: 12.w,
              height: 12.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: AppTheme.lightTheme.colorScheme.error,
                  width: 2,
                ),
              ),
              child: ClipOval(
                child: CustomImageWidget(
                  imageUrl: fromAvatar,
                  width: 12.w,
                  height: 12.w,
                  fit: BoxFit.cover,
                ),
              ),
            ),
            SizedBox(width: 3.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  RichText(
                    text: TextSpan(
                      style: AppTheme.lightTheme.textTheme.bodyMedium,
                      children: [
                        TextSpan(
                          text: fromUser,
                          style: TextStyle(fontWeight: FontWeight.w600),
                        ),
                        TextSpan(text: " owes "),
                        TextSpan(
                          text: toUser,
                          style: TextStyle(fontWeight: FontWeight.w600),
                        ),
                      ],
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 0.5.h),
                  Text(
                    "\$${amount.toStringAsFixed(2)}",
                    style: AppTheme.getCurrencyStyle(
                      isLight: true,
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.lightTheme.colorScheme.error,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(width: 3.w),
            CustomIconWidget(
              iconName: 'arrow_forward',
              color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
              size: 6.w,
            ),
            SizedBox(width: 3.w),
            // To user avatar
            Container(
              width: 12.w,
              height: 12.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: AppTheme.lightTheme.colorScheme.tertiary,
                  width: 2,
                ),
              ),
              child: ClipOval(
                child: CustomImageWidget(
                  imageUrl: toAvatar,
                  width: 12.w,
                  height: 12.w,
                  fit: BoxFit.cover,
                ),
              ),
            ),
            SizedBox(width: 4.w),
            ElevatedButton(
              onPressed: onSettleUp,
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
                minimumSize: Size(0, 5.h),
              ),
              child: Text(
                "Settle Up",
                style: TextStyle(fontSize: 12.sp),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
