import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import './widgets/activity_item_widget.dart';
import './widgets/balance_item_widget.dart';
import './widgets/empty_state_widget.dart';
import './widgets/expense_item_widget.dart';
import './widgets/group_header_widget.dart';
import './widgets/member_list_widget.dart';

class GroupDetails extends StatefulWidget {
  const GroupDetails({Key? key}) : super(key: key);

  @override
  State<GroupDetails> createState() => _GroupDetailsState();
}

class _GroupDetailsState extends State<GroupDetails>
    with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;
  int _selectedTabIndex = 0;

  // Mock data for the group
  final Map<String, dynamic> _groupData = {
    "id": 1,
    "name": "Europe Trip 2025",
    "coverImage":
        "https://images.unsplash.com/photo-1488646953014-85cb44e25828?fm=jpg&q=60&w=3000&ixlib=rb-4.0.3",
    "createdDate": DateTime.now().subtract(Duration(days: 15)),
    "members": [
      {
        "id": 1,
        "name": "Alex Johnson",
        "avatar":
            "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png",
        "balance": 125.50,
      },
      {
        "id": 2,
        "name": "Sarah Wilson",
        "avatar":
            "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png",
        "balance": -75.25,
      },
      {
        "id": 3,
        "name": "Mike Chen",
        "avatar":
            "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png",
        "balance": 0.0,
      },
      {
        "id": 4,
        "name": "Emma Davis",
        "avatar":
            "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png",
        "balance": -50.25,
      },
    ],
  };

  final List<Map<String, dynamic>> _expenses = [
    {
      "id": 1,
      "description": "Dinner at Italian Restaurant",
      "amount": 120.50,
      "category": "Food",
      "paidBy": "Alex Johnson",
      "date": DateTime.now().subtract(Duration(hours: 2)),
      "isFavorite": true,
    },
    {
      "id": 2,
      "description": "Uber to Airport",
      "amount": 45.75,
      "category": "Transport",
      "paidBy": "Sarah Wilson",
      "date": DateTime.now().subtract(Duration(days: 1)),
      "isFavorite": false,
    },
    {
      "id": 3,
      "description": "Hotel Booking - 3 nights",
      "amount": 450.00,
      "category": "Accommodation",
      "paidBy": "Mike Chen",
      "date": DateTime.now().subtract(Duration(days: 2)),
      "isFavorite": false,
    },
    {
      "id": 4,
      "description": "Movie Tickets",
      "amount": 60.00,
      "category": "Entertainment",
      "paidBy": "Emma Davis",
      "date": DateTime.now().subtract(Duration(days: 3)),
      "isFavorite": true,
    },
    {
      "id": 5,
      "description": "Grocery Shopping",
      "amount": 85.30,
      "category": "Shopping",
      "paidBy": "Alex Johnson",
      "date": DateTime.now().subtract(Duration(days: 4)),
      "isFavorite": false,
    },
  ];

  final List<Map<String, dynamic>> _balances = [
    {
      "fromUser": "Sarah Wilson",
      "toUser": "Alex Johnson",
      "amount": 75.25,
      "fromAvatar":
          "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png",
      "toAvatar":
          "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png",
    },
    {
      "fromUser": "Emma Davis",
      "toUser": "Alex Johnson",
      "amount": 50.25,
      "fromAvatar":
          "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png",
      "toAvatar":
          "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png",
    },
  ];

  final List<Map<String, dynamic>> _activities = [
    {
      "id": 1,
      "type": "expense_added",
      "user": "Alex Johnson",
      "description": "added expense \"Dinner at Italian Restaurant\"",
      "timestamp": DateTime.now().subtract(Duration(hours: 2)),
      "avatar":
          "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png",
      "amount": 120.50,
    },
    {
      "id": 2,
      "type": "payment_made",
      "user": "Sarah Wilson",
      "description": "made a payment to Alex Johnson",
      "timestamp": DateTime.now().subtract(Duration(hours: 5)),
      "avatar":
          "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png",
      "amount": 25.00,
    },
    {
      "id": 3,
      "type": "expense_added",
      "user": "Sarah Wilson",
      "description": "added expense \"Uber to Airport\"",
      "timestamp": DateTime.now().subtract(Duration(days: 1)),
      "avatar":
          "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png",
      "amount": 45.75,
    },
    {
      "id": 4,
      "type": "member_added",
      "user": "Mike Chen",
      "description": "joined the group",
      "timestamp": DateTime.now().subtract(Duration(days: 2)),
      "avatar":
          "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png",
      "amount": null,
    },
    {
      "id": 5,
      "type": "expense_added",
      "user": "Mike Chen",
      "description": "added expense \"Hotel Booking - 3 nights\"",
      "timestamp": DateTime.now().subtract(Duration(days: 2)),
      "avatar":
          "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png",
      "amount": 450.00,
    },
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(() {
      setState(() {
        _selectedTabIndex = _tabController.index;
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _refreshData() async {
    setState(() {
      _isLoading = true;
    });

    // Simulate API call
    await Future.delayed(Duration(seconds: 1));

    setState(() {
      _isLoading = false;
    });
  }

  void _showGroupMenu() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: EdgeInsets.all(4.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: CustomIconWidget(
                iconName: 'group_add',
                color: AppTheme.lightTheme.colorScheme.primary,
                size: 6.w,
              ),
              title: Text("Manage Members"),
              onTap: () {
                Navigator.pop(context);
                // Navigate to member management
              },
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'settings',
                color: AppTheme.lightTheme.colorScheme.primary,
                size: 6.w,
              ),
              title: Text("Group Settings"),
              onTap: () {
                Navigator.pop(context);
                // Navigate to group settings
              },
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'download',
                color: AppTheme.lightTheme.colorScheme.primary,
                size: 6.w,
              ),
              title: Text("Export Data"),
              onTap: () {
                Navigator.pop(context);
                _exportGroupData();
              },
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'delete',
                color: AppTheme.lightTheme.colorScheme.error,
                size: 6.w,
              ),
              title: Text("Leave Group"),
              onTap: () {
                Navigator.pop(context);
                _showLeaveGroupDialog();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _exportGroupData() {
    // Export functionality would be implemented here
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text("Group data exported successfully")),
    );
  }

  void _showLeaveGroupDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text("Leave Group"),
        content: Text(
            "Are you sure you want to leave this group? You won't be able to see group expenses anymore."),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text("Cancel"),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
            },
            child: Text("Leave"),
          ),
        ],
      ),
    );
  }

  void _onMemberTap(Map<String, dynamic> member) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: EdgeInsets.all(4.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Container(
                  width: 15.w,
                  height: 15.w,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: AppTheme.lightTheme.colorScheme.outline,
                      width: 2,
                    ),
                  ),
                  child: ClipOval(
                    child: CustomImageWidget(
                      imageUrl: member["avatar"] as String,
                      width: 15.w,
                      height: 15.w,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                SizedBox(width: 4.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        member["name"] as String,
                        style:
                            AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        "Balance: \$${(member["balance"] as double).toStringAsFixed(2)}",
                        style: AppTheme.getCurrencyStyle(
                          isLight: true,
                          fontSize: 14.sp,
                          color: (member["balance"] as double) >= 0
                              ? AppTheme.lightTheme.colorScheme.tertiary
                              : AppTheme.lightTheme.colorScheme.error,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 3.h),
            if ((member["balance"] as double) != 0)
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  Navigator.pushNamed(context, '/settlement-screen');
                },
                child: Text("Settle Balance"),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildExpensesTab() {
    if (_expenses.isEmpty) {
      return EmptyStateWidget(
        title: "No Expenses Yet",
        subtitle: "Start adding expenses to track your group spending",
        iconName: 'receipt_long',
        buttonText: "Add Expense",
        onButtonPressed: () => Navigator.pushNamed(context, '/add-expense'),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.only(top: 2.h, bottom: 10.h),
      itemCount: _expenses.length,
      itemBuilder: (context, index) {
        final expense = _expenses[index];
        return ExpenseItemWidget(
          expense: expense,
          onTap: () {
            // Navigate to expense details
          },
          onEdit: () {
            Navigator.pushNamed(context, '/add-expense');
          },
          onDelete: () {
            setState(() {
              _expenses.removeAt(index);
            });
          },
          onFavorite: () {
            setState(() {
              expense["isFavorite"] =
                  !(expense["isFavorite"] as bool? ?? false);
            });
          },
        );
      },
    );
  }

  Widget _buildBalancesTab() {
    if (_balances.isEmpty) {
      return EmptyStateWidget(
        title: "All Settled Up!",
        subtitle:
            "Everyone's balances are settled. Great job managing your expenses!",
        iconName: 'account_balance_wallet',
      );
    }

    return ListView.builder(
      padding: EdgeInsets.only(top: 2.h, bottom: 10.h),
      itemCount: _balances.length,
      itemBuilder: (context, index) {
        final balance = _balances[index];
        return BalanceItemWidget(
          balanceData: balance,
          onSettleUp: () {
            Navigator.pushNamed(context, '/settlement-screen');
          },
        );
      },
    );
  }

  Widget _buildActivityTab() {
    if (_activities.isEmpty) {
      return EmptyStateWidget(
        title: "No Activity Yet",
        subtitle:
            "Group activity will appear here as members add expenses and make payments",
        iconName: 'timeline',
      );
    }

    return ListView.builder(
      padding: EdgeInsets.only(top: 2.h, bottom: 10.h),
      itemCount: _activities.length,
      itemBuilder: (context, index) {
        final activity = _activities[index];
        return ActivityItemWidget(activity: activity);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: _refreshData,
        child: CustomScrollView(
          slivers: [
            SliverToBoxAdapter(
              child: GroupHeaderWidget(
                groupData: _groupData,
                onMenuTap: _showGroupMenu,
              ),
            ),
            SliverToBoxAdapter(
              child: Container(
                color: AppTheme.lightTheme.scaffoldBackgroundColor,
                child: Column(
                  children: [
                    SizedBox(height: 2.h),
                    MemberListWidget(
                      members:
                          _groupData["members"] as List<Map<String, dynamic>>,
                      onMemberTap: _onMemberTap,
                    ),
                    SizedBox(height: 2.h),
                    Container(
                      margin: EdgeInsets.symmetric(horizontal: 4.w),
                      decoration: BoxDecoration(
                        color: AppTheme.lightTheme.colorScheme.surfaceContainerHighest,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: TabBar(
                        controller: _tabController,
                        indicator: BoxDecoration(
                          color: AppTheme.lightTheme.colorScheme.primary,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        labelColor: Colors.white,
                        unselectedLabelColor:
                            AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                        dividerColor: Colors.transparent,
                        tabs: [
                          Tab(text: "Expenses"),
                          Tab(text: "Balances"),
                          Tab(text: "Activity"),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SliverFillRemaining(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildExpensesTab(),
                  _buildBalancesTab(),
                  _buildActivityTab(),
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: _selectedTabIndex == 0
          ? FloatingActionButton.extended(
              onPressed: () => Navigator.pushNamed(context, '/add-expense'),
              icon: CustomIconWidget(
                iconName: 'add',
                color: Colors.white,
                size: 6.w,
              ),
              label: Text("Add Expense"),
            )
          : null,
    );
  }
}
