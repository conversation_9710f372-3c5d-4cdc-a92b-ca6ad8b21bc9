import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class SortBottomSheetWidget extends StatefulWidget {
  final String currentSortBy;
  final bool currentSortAscending;
  final Function(String, bool) onSortChanged;

  const SortBottomSheetWidget({
    Key? key,
    required this.currentSortBy,
    required this.currentSortAscending,
    required this.onSortChanged,
  }) : super(key: key);

  @override
  State<SortBottomSheetWidget> createState() => _SortBottomSheetWidgetState();
}

class _SortBottomSheetWidgetState extends State<SortBottomSheetWidget> {
  late String _selectedSortBy;
  late bool _isAscending;

  final List<Map<String, dynamic>> _sortOptions = [
    {
      'key': 'date',
      'title': 'Date',
      'icon': 'date_range',
      'ascendingLabel': 'Oldest First',
      'descendingLabel': 'Newest First',
    },
    {
      'key': 'amount',
      'title': 'Amount',
      'icon': 'attach_money',
      'ascendingLabel': 'Low to High',
      'descendingLabel': 'High to Low',
    },
    {
      'key': 'group',
      'title': 'Group Name',
      'icon': 'group',
      'ascendingLabel': 'A to Z',
      'descendingLabel': 'Z to A',
    },
    {
      'key': 'category',
      'title': 'Category',
      'icon': 'category',
      'ascendingLabel': 'A to Z',
      'descendingLabel': 'Z to A',
    },
  ];

  @override
  void initState() {
    super.initState();
    _selectedSortBy = widget.currentSortBy;
    _isAscending = widget.currentSortAscending;
  }

  void _applySorting() {
    widget.onSortChanged(_selectedSortBy, _isAscending);
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    final bool isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      height: 60.h,
      decoration: BoxDecoration(
        color: isDark ? AppTheme.surfaceDark : AppTheme.surfaceLight,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 12.w,
            height: 0.5.h,
            margin: EdgeInsets.only(top: 2.h),
            decoration: BoxDecoration(
              color: isDark
                  ? AppTheme.onSurfaceVariantDark
                  : AppTheme.onSurfaceVariantLight,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: EdgeInsets.all(4.w),
            child: Row(
              children: [
                Text(
                  'Sort Expenses',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(
                    'Cancel',
                    style: TextStyle(
                      color: isDark
                          ? AppTheme.onSurfaceVariantDark
                          : AppTheme.onSurfaceVariantLight,
                    ),
                  ),
                ),
              ],
            ),
          ),

          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.symmetric(horizontal: 4.w),
              itemCount: _sortOptions.length,
              itemBuilder: (context, index) {
                final option = _sortOptions[index];
                final isSelected = _selectedSortBy == option['key'];

                return Container(
                  margin: EdgeInsets.only(bottom: 2.h),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: isSelected
                          ? (isDark
                              ? AppTheme.primaryDark
                              : AppTheme.primaryLight)
                          : (isDark
                              ? AppTheme.outlineDark
                              : AppTheme.outlineLight),
                      width: isSelected ? 2 : 1,
                    ),
                    borderRadius: BorderRadius.circular(12),
                    color: isSelected
                        ? (isDark
                                ? AppTheme.primaryDark
                                : AppTheme.primaryLight)
                            .withValues(alpha: 0.05)
                        : Colors.transparent,
                  ),
                  child: Column(
                    children: [
                      ListTile(
                        leading: Container(
                          width: 10.w,
                          height: 10.w,
                          decoration: BoxDecoration(
                            color: isSelected
                                ? (isDark
                                        ? AppTheme.primaryDark
                                        : AppTheme.primaryLight)
                                    .withValues(alpha: 0.1)
                                : (isDark
                                    ? AppTheme.surfaceVariantDark
                                    : AppTheme.surfaceVariantLight),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Center(
                            child: CustomIconWidget(
                              iconName: option['icon'],
                              color: isSelected
                                  ? (isDark
                                      ? AppTheme.primaryDark
                                      : AppTheme.primaryLight)
                                  : (isDark
                                      ? AppTheme.onSurfaceVariantDark
                                      : AppTheme.onSurfaceVariantLight),
                              size: 5.w,
                            ),
                          ),
                        ),
                        title: Text(
                          option['title'],
                          style:
                              Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: isSelected
                                        ? FontWeight.w600
                                        : FontWeight.w500,
                                    color: isSelected
                                        ? (isDark
                                            ? AppTheme.primaryDark
                                            : AppTheme.primaryLight)
                                        : null,
                                  ),
                        ),
                        trailing: isSelected
                            ? CustomIconWidget(
                                iconName: 'check_circle',
                                color: isDark
                                    ? AppTheme.primaryDark
                                    : AppTheme.primaryLight,
                                size: 6.w,
                              )
                            : null,
                        onTap: () {
                          setState(() {
                            _selectedSortBy = option['key'];
                          });
                        },
                      ),
                      if (isSelected) ...[
                        Divider(
                          color: isDark
                              ? AppTheme.outlineDark
                              : AppTheme.outlineLight,
                          height: 1,
                        ),
                        Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: 4.w, vertical: 2.h),
                          child: Row(
                            children: [
                              Expanded(
                                child: GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      _isAscending = true;
                                    });
                                  },
                                  child: Container(
                                    padding:
                                        EdgeInsets.symmetric(vertical: 2.h),
                                    decoration: BoxDecoration(
                                      color: _isAscending
                                          ? (isDark
                                              ? AppTheme.primaryDark
                                              : AppTheme.primaryLight)
                                          : Colors.transparent,
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                        color: _isAscending
                                            ? (isDark
                                                ? AppTheme.primaryDark
                                                : AppTheme.primaryLight)
                                            : (isDark
                                                ? AppTheme.outlineDark
                                                : AppTheme.outlineLight),
                                      ),
                                    ),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        CustomIconWidget(
                                          iconName: 'arrow_upward',
                                          color: _isAscending
                                              ? Colors.white
                                              : (isDark
                                                  ? AppTheme
                                                      .onSurfaceVariantDark
                                                  : AppTheme
                                                      .onSurfaceVariantLight),
                                          size: 4.w,
                                        ),
                                        SizedBox(width: 2.w),
                                        Text(
                                          option['ascendingLabel'],
                                          style: TextStyle(
                                            color: _isAscending
                                                ? Colors.white
                                                : (isDark
                                                    ? AppTheme.onSurfaceDark
                                                    : AppTheme.onSurfaceLight),
                                            fontWeight: FontWeight.w500,
                                            fontSize: 12.sp,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(width: 3.w),
                              Expanded(
                                child: GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      _isAscending = false;
                                    });
                                  },
                                  child: Container(
                                    padding:
                                        EdgeInsets.symmetric(vertical: 2.h),
                                    decoration: BoxDecoration(
                                      color: !_isAscending
                                          ? (isDark
                                              ? AppTheme.primaryDark
                                              : AppTheme.primaryLight)
                                          : Colors.transparent,
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                        color: !_isAscending
                                            ? (isDark
                                                ? AppTheme.primaryDark
                                                : AppTheme.primaryLight)
                                            : (isDark
                                                ? AppTheme.outlineDark
                                                : AppTheme.outlineLight),
                                      ),
                                    ),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        CustomIconWidget(
                                          iconName: 'arrow_downward',
                                          color: !_isAscending
                                              ? Colors.white
                                              : (isDark
                                                  ? AppTheme
                                                      .onSurfaceVariantDark
                                                  : AppTheme
                                                      .onSurfaceVariantLight),
                                          size: 4.w,
                                        ),
                                        SizedBox(width: 2.w),
                                        Text(
                                          option['descendingLabel'],
                                          style: TextStyle(
                                            color: !_isAscending
                                                ? Colors.white
                                                : (isDark
                                                    ? AppTheme.onSurfaceDark
                                                    : AppTheme.onSurfaceLight),
                                            fontWeight: FontWeight.w500,
                                            fontSize: 12.sp,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                );
              },
            ),
          ),

          // Apply Button
          Container(
            padding: EdgeInsets.all(4.w),
            child: SizedBox(
              width: double.infinity,
              height: 6.h,
              child: ElevatedButton(
                onPressed: _applySorting,
                child: Text(
                  'Apply Sorting',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
