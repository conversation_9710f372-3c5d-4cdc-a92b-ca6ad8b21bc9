import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class MemberListWidget extends StatelessWidget {
  final List<Map<String, dynamic>> members;
  final Function(Map<String, dynamic>) onMemberTap;

  const MemberListWidget({
    Key? key,
    required this.members,
    required this.onMemberTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 12.h,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: 4.w),
        itemCount: members.length,
        itemBuilder: (context, index) {
          final member = members[index];
          final balance = member["balance"] as double;

          return GestureDetector(
            onTap: () => onMemberTap(member),
            child: Container(
              width: 20.w,
              margin: EdgeInsets.only(right: 3.w),
              child: Column(
                children: [
                  Stack(
                    children: [
                      Container(
                        width: 15.w,
                        height: 15.w,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: balance > 0
                                ? AppTheme.lightTheme.colorScheme.tertiary
                                : balance < 0
                                    ? AppTheme.lightTheme.colorScheme.error
                                    : AppTheme.lightTheme.colorScheme.outline,
                            width: 2,
                          ),
                        ),
                        child: ClipOval(
                          child: CustomImageWidget(
                            imageUrl: member["avatar"] as String,
                            width: 15.w,
                            height: 15.w,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                      if (balance != 0)
                        Positioned(
                          right: 0,
                          bottom: 0,
                          child: Container(
                            width: 5.w,
                            height: 5.w,
                            decoration: BoxDecoration(
                              color: balance > 0
                                  ? AppTheme.lightTheme.colorScheme.tertiary
                                  : AppTheme.lightTheme.colorScheme.error,
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: AppTheme.lightTheme.colorScheme.surface,
                                width: 1,
                              ),
                            ),
                            child: CustomIconWidget(
                              iconName: balance > 0
                                  ? 'arrow_upward'
                                  : 'arrow_downward',
                              color: Colors.white,
                              size: 3.w,
                            ),
                          ),
                        ),
                    ],
                  ),
                  SizedBox(height: 1.h),
                  Text(
                    (member["name"] as String).split(' ').first,
                    style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                  ),
                  Text(
                    balance == 0
                        ? "settled"
                        : balance > 0
                            ? "+\$${balance.toStringAsFixed(2)}"
                            : "-\$${balance.abs().toStringAsFixed(2)}",
                    style: AppTheme.getCurrencyStyle(
                      isLight: true,
                      fontSize: 10.sp,
                      fontWeight: FontWeight.w600,
                      color: balance > 0
                          ? AppTheme.lightTheme.colorScheme.tertiary
                          : balance < 0
                              ? AppTheme.lightTheme.colorScheme.error
                              : AppTheme
                                  .lightTheme.colorScheme.onSurfaceVariant,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
