import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import './widgets/payment_history_section.dart';
import './widgets/payment_methods_section.dart';
import './widgets/settlement_confirmation_dialog.dart';
import './widgets/settlement_item_card.dart';
import './widgets/settlement_summary_card.dart';
import './widgets/success_animation_widget.dart';

class SettlementScreen extends StatefulWidget {
  const SettlementScreen({Key? key}) : super(key: key);

  @override
  State<SettlementScreen> createState() => _SettlementScreenState();
}

class _SettlementScreenState extends State<SettlementScreen> {
  String selectedPaymentMethod = 'cash';
  String customNote = '';
  bool showSuccessAnimation = false;

  // Mock data for settlement summary
  final Map<String, dynamic> settlementSummary = {
    'totalAmount': 245.75,
    'currency': 'USD',
    'involvedMembers': [
      {
        'id': 1,
        'name': '<PERSON>',
        'avatar':
            'https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png'
      },
      {
        'id': 2,
        'name': '<PERSON>',
        'avatar':
            'https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png'
      },
      {
        'id': 3,
        'name': 'Mike Johnson',
        'avatar':
            'https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png'
      },
      {
        'id': 4,
        'name': 'Emma Davis',
        'avatar':
            'https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png'
      },
    ],
  };

  // Mock data for settlement suggestions
  List<Map<String, dynamic>> settlementSuggestions = [
    {
      'id': 1,
      'payerName': 'John Doe',
      'payeeName': 'Sarah Wilson',
      'payerAvatar':
          'https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png',
      'payeeAvatar':
          'https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png',
      'amount': 85.50,
      'currency': 'USD',
      'isPaid': false,
      'note': 'Dinner and movie tickets',
    },
    {
      'id': 2,
      'payerName': 'Mike Johnson',
      'payeeName': 'Emma Davis',
      'payerAvatar':
          'https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png',
      'payeeAvatar':
          'https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png',
      'amount': 42.25,
      'currency': 'USD',
      'isPaid': false,
      'note': '',
    },
    {
      'id': 3,
      'payerName': 'Sarah Wilson',
      'payeeName': 'John Doe',
      'payerAvatar':
          'https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png',
      'payeeAvatar':
          'https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png',
      'amount': 118.00,
      'currency': 'USD',
      'isPaid': false,
      'note': 'Hotel booking share',
    },
  ];

  // Mock data for payment history
  final List<Map<String, dynamic>> paymentHistory = [
    {
      'id': 1,
      'payerName': 'Emma Davis',
      'payeeName': 'Mike Johnson',
      'amount': 67.30,
      'method': 'Bank Transfer',
      'timestamp': DateTime.now().subtract(const Duration(hours: 2)),
      'hasReceipt': true,
      'note': 'Gas and parking fees',
    },
    {
      'id': 2,
      'payerName': 'John Doe',
      'payeeName': 'Emma Davis',
      'amount': 25.75,
      'method': 'Digital Wallet',
      'timestamp': DateTime.now().subtract(const Duration(days: 1)),
      'hasReceipt': false,
      'note': 'Coffee and snacks',
    },
    {
      'id': 3,
      'payerName': 'Sarah Wilson',
      'payeeName': 'Mike Johnson',
      'amount': 156.40,
      'method': 'Cash',
      'timestamp': DateTime.now().subtract(const Duration(days: 2)),
      'hasReceipt': true,
      'note': '',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      appBar: _buildAppBar(),
      body: showSuccessAnimation
          ? SuccessAnimationWidget(
              onComplete: () {
                setState(() {
                  showSuccessAnimation = false;
                });
              },
            )
          : _buildBody(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppTheme.lightTheme.colorScheme.surface,
      elevation: 0,
      leading: IconButton(
        onPressed: () => Navigator.pop(context),
        icon: CustomIconWidget(
          iconName: 'arrow_back',
          color: AppTheme.lightTheme.colorScheme.onSurface,
          size: 24,
        ),
      ),
      title: Text(
        'Settle Up',
        style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.w600,
          color: AppTheme.lightTheme.colorScheme.onSurface,
        ),
      ),
      centerTitle: true,
      actions: [
        IconButton(
          onPressed: _showShareOptions,
          icon: CustomIconWidget(
            iconName: 'share',
            color: AppTheme.lightTheme.colorScheme.onSurface,
            size: 24,
          ),
        ),
        IconButton(
          onPressed: _showMoreOptions,
          icon: CustomIconWidget(
            iconName: 'more_vert',
            color: AppTheme.lightTheme.colorScheme.onSurface,
            size: 24,
          ),
        ),
      ],
    );
  }

  Widget _buildBody() {
    return RefreshIndicator(
      onRefresh: _refreshData,
      color: AppTheme.lightTheme.colorScheme.primary,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Settlement Summary Card
            SettlementSummaryCard(settlementData: settlementSummary),

            // Settlement Suggestions Section
            _buildSectionHeader(
                'Settlement Suggestions', 'Optimized for fewer transactions'),

            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: settlementSuggestions.length,
              itemBuilder: (context, index) {
                final settlement = settlementSuggestions[index];
                return SettlementItemCard(
                  settlement: settlement,
                  onMarkAsPaid: () => _markAsPaid(settlement),
                  onEdit: () => _editSettlement(settlement),
                  onAddNote: () => _addNote(settlement),
                  onRequestReminder: () => _requestReminder(settlement),
                  onLongPress: () => _showDetailedBreakdown(settlement),
                );
              },
            ),

            // Payment Methods Section
            PaymentMethodsSection(
              selectedMethod: selectedPaymentMethod,
              onMethodChanged: (method) {
                setState(() {
                  selectedPaymentMethod = method;
                });
              },
              customNote: customNote,
              onNoteChanged: (note) {
                setState(() {
                  customNote = note;
                });
              },
            ),

            // Payment History Section
            PaymentHistorySection(paymentHistory: paymentHistory),

            SizedBox(height: 10.h), // Space for FAB
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, String subtitle) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
              color: AppTheme.lightTheme.colorScheme.onSurface,
            ),
          ),
          SizedBox(height: 0.5.h),
          Text(
            subtitle,
            style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
              color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: _settleAllDebts,
      backgroundColor: AppTheme.lightTheme.colorScheme.primary,
      foregroundColor: Colors.white,
      icon: CustomIconWidget(
        iconName: 'payment',
        color: Colors.white,
        size: 24,
      ),
      label: Text(
        'Settle All',
        style: AppTheme.lightTheme.textTheme.labelLarge?.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Future<void> _refreshData() async {
    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));

    // Refresh settlement data
    setState(() {
      // In a real app, this would fetch fresh data from the server
    });
  }

  void _markAsPaid(Map<String, dynamic> settlement) {
    _showConfirmationDialog(settlement);
  }

  void _showConfirmationDialog(Map<String, dynamic> settlement) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => SettlementConfirmationDialog(
        settlement: settlement,
        paymentMethod: selectedPaymentMethod,
        customNote: customNote,
        onConfirm: () {
          Navigator.pop(context);
          _confirmPayment(settlement);
        },
        onCancel: () {
          Navigator.pop(context);
        },
      ),
    );
  }

  void _confirmPayment(Map<String, dynamic> settlement) {
    // Haptic feedback
    HapticFeedback.mediumImpact();

    setState(() {
      showSuccessAnimation = true;

      // Update settlement status
      final index =
          settlementSuggestions.indexWhere((s) => s['id'] == settlement['id']);
      if (index != -1) {
        settlementSuggestions[index]['isPaid'] = true;

        // Add to payment history
        paymentHistory.insert(0, {
          'id': DateTime.now().millisecondsSinceEpoch,
          'payerName': settlement['payerName'],
          'payeeName': settlement['payeeName'],
          'amount': settlement['amount'],
          'method': _getPaymentMethodName(selectedPaymentMethod),
          'timestamp': DateTime.now(),
          'hasReceipt': selectedPaymentMethod != 'cash',
          'note': customNote.isNotEmpty ? customNote : settlement['note'] ?? '',
        });
      }
    });
  }

  String _getPaymentMethodName(String method) {
    switch (method) {
      case 'cash':
        return 'Cash';
      case 'bank_transfer':
        return 'Bank Transfer';
      case 'digital_wallet':
        return 'Digital Wallet';
      case 'other':
        return 'Other';
      default:
        return 'Unknown';
    }
  }

  void _editSettlement(Map<String, dynamic> settlement) {
    // Show edit dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Edit Settlement',
          style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Text(
          'Edit settlement amount or split details',
          style: AppTheme.lightTheme.textTheme.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Implement edit functionality
            },
            child: Text('Save Changes'),
          ),
        ],
      ),
    );
  }

  void _addNote(Map<String, dynamic> settlement) {
    final noteController =
        TextEditingController(text: settlement['note'] ?? '');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Add Note',
          style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        content: TextField(
          controller: noteController,
          maxLines: 3,
          decoration: InputDecoration(
            hintText: 'Add a note for this settlement...',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                final index = settlementSuggestions
                    .indexWhere((s) => s['id'] == settlement['id']);
                if (index != -1) {
                  settlementSuggestions[index]['note'] = noteController.text;
                }
              });
              Navigator.pop(context);
            },
            child: Text('Save Note'),
          ),
        ],
      ),
    );
  }

  void _requestReminder(Map<String, dynamic> settlement) {
    HapticFeedback.lightImpact();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Reminder sent to ${settlement['payerName']}',
          style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
            color: Colors.white,
          ),
        ),
        backgroundColor: AppTheme.lightTheme.colorScheme.primary,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  void _showDetailedBreakdown(Map<String, dynamic> settlement) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: 60.h,
        decoration: BoxDecoration(
          color: AppTheme.lightTheme.colorScheme.surface,
          borderRadius: const BorderRadius.vertical(
            top: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            Container(
              width: 12.w,
              height: 0.5.h,
              margin: EdgeInsets.only(top: 2.h),
              decoration: BoxDecoration(
                color: AppTheme.lightTheme.colorScheme.outline,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Padding(
              padding: EdgeInsets.all(4.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Settlement Breakdown',
                    style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    'This settlement includes expenses from:',
                    style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                      color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                  SizedBox(height: 2.h),
                  // Mock expense breakdown
                  _buildExpenseBreakdownItem('Restaurant Bill', 45.50),
                  _buildExpenseBreakdownItem('Movie Tickets', 28.00),
                  _buildExpenseBreakdownItem('Parking Fee', 12.00),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExpenseBreakdownItem(String description, double amount) {
    return Container(
      margin: EdgeInsets.only(bottom: 2.h),
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            description,
            style: AppTheme.lightTheme.textTheme.bodyMedium,
          ),
          Text(
            '\$${amount.toStringAsFixed(2)}',
            style: AppTheme.getCurrencyStyle(
              isLight: true,
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  void _settleAllDebts() {
    final unpaidSettlements =
        settlementSuggestions.where((s) => !s['isPaid']).toList();

    if (unpaidSettlements.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('All settlements are already completed!'),
          backgroundColor: AppTheme.successLight,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Settle All Debts',
          style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Text(
          'This will mark all ${unpaidSettlements.length} pending settlements as paid. Continue?',
          style: AppTheme.lightTheme.textTheme.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _confirmAllPayments();
            },
            child: Text('Settle All'),
          ),
        ],
      ),
    );
  }

  void _confirmAllPayments() {
    setState(() {
      showSuccessAnimation = true;

      // Mark all as paid and add to history
      for (var settlement in settlementSuggestions) {
        if (!settlement['isPaid']) {
          settlement['isPaid'] = true;

          paymentHistory.insert(0, {
            'id': DateTime.now().millisecondsSinceEpoch + settlement['id'],
            'payerName': settlement['payerName'],
            'payeeName': settlement['payeeName'],
            'amount': settlement['amount'],
            'method': _getPaymentMethodName(selectedPaymentMethod),
            'timestamp': DateTime.now(),
            'hasReceipt': selectedPaymentMethod != 'cash',
            'note': settlement['note'] ?? '',
          });
        }
      }
    });
  }

  void _showShareOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: EdgeInsets.all(4.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Share Settlement',
              style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 2.h),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'message',
                color: AppTheme.lightTheme.colorScheme.primary,
                size: 24,
              ),
              title: Text('Send via Message'),
              onTap: () {
                Navigator.pop(context);
                // Implement message sharing
              },
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'email',
                color: AppTheme.lightTheme.colorScheme.primary,
                size: 24,
              ),
              title: Text('Send via Email'),
              onTap: () {
                Navigator.pop(context);
                // Implement email sharing
              },
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'copy',
                color: AppTheme.lightTheme.colorScheme.primary,
                size: 24,
              ),
              title: Text('Copy Link'),
              onTap: () {
                Navigator.pop(context);
                Clipboard.setData(
                    const ClipboardData(text: 'Settlement link copied'));
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                      content: Text('Settlement link copied to clipboard')),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showMoreOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: EdgeInsets.all(4.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'More Options',
              style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 2.h),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'download',
                color: AppTheme.lightTheme.colorScheme.primary,
                size: 24,
              ),
              title: Text('Export Report'),
              onTap: () {
                Navigator.pop(context);
                // Implement export functionality
              },
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'refresh',
                color: AppTheme.lightTheme.colorScheme.primary,
                size: 24,
              ),
              title: Text('Recalculate'),
              onTap: () {
                Navigator.pop(context);
                _refreshData();
              },
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'settings',
                color: AppTheme.lightTheme.colorScheme.primary,
                size: 24,
              ),
              title: Text('Settlement Settings'),
              onTap: () {
                Navigator.pop(context);
                // Navigate to settings
              },
            ),
          ],
        ),
      ),
    );
  }
}
