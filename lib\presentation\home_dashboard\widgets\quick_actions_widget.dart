import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class QuickActionsWidget extends StatelessWidget {
  final VoidCallback? onAddExpense;
  final VoidCallback? onCreateGroup;
  final VoidCallback? onViewExpenses;
  final VoidCallback? onSettleUp;

  const QuickActionsWidget({
    Key? key,
    this.onAddExpense,
    this.onCreateGroup,
    this.onViewExpenses,
    this.onSettleUp,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quick Actions',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  fontSize: 16.sp,
                ),
          ),
          Sized<PERSON><PERSON>(height: 2.h),
          Row(
            children: [
              Expanded(
                child: _buildActionCard(
                  context,
                  title: 'Add Expense',
                  subtitle: 'Split a new bill',
                  icon: 'add_circle',
                  color: AppTheme.lightTheme.primaryColor,
                  onTap: onAddExpense ??
                      () => Navigator.pushNamed(context, '/add-expense'),
                ),
              ),
              SizedBox(width: 3.w),
              Expanded(
                child: _buildActionCard(
                  context,
                  title: 'Create Group',
                  subtitle: 'Start new group',
                  icon: 'group_add',
                  color: AppTheme.secondaryLight,
                  onTap: onCreateGroup ??
                      () => Navigator.pushNamed(context, '/groups-list'),
                ),
              ),
            ],
          ),
          SizedBox(height: 2.h),
          Row(
            children: [
              Expanded(
                child: _buildActionCard(
                  context,
                  title: 'View Expenses',
                  subtitle: 'See all transactions',
                  icon: 'receipt_long',
                  color: AppTheme.warningLight,
                  onTap: onViewExpenses ??
                      () => Navigator.pushNamed(context, '/expenses-list'),
                ),
              ),
              SizedBox(width: 3.w),
              Expanded(
                child: _buildActionCard(
                  context,
                  title: 'Settle Up',
                  subtitle: 'Pay or request',
                  icon: 'account_balance_wallet',
                  color: AppTheme.successLight,
                  onTap: onSettleUp ??
                      () => Navigator.pushNamed(context, '/settlement-screen'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required String icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(4.w),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: color.withValues(alpha: 0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: 12.w,
              height: 12.w,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: CustomIconWidget(
                iconName: icon,
                color: color,
                size: 24,
              ),
            ),
            SizedBox(height: 2.h),
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    fontSize: 14.sp,
                  ),
            ),
            SizedBox(height: 0.5.h),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: isDark
                        ? AppTheme.onSurfaceVariantDark
                        : AppTheme.onSurfaceVariantLight,
                    fontSize: 11.sp,
                  ),
            ),
          ],
        ),
      ),
    );
  }
}
