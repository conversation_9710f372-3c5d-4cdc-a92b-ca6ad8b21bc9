import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class EmptyStateWidget extends StatelessWidget {
  final VoidCallback? onAddExpense;

  const EmptyStateWidget({
    Key? key,
    this.onAddExpense,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bool isDark = Theme.of(context).brightness == Brightness.dark;

    return Center(
      child: Padding(
        padding: EdgeInsets.all(8.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Illustration
            Container(
              width: 60.w,
              height: 40.h,
              decoration: BoxDecoration(
                color: isDark
                    ? AppTheme.primaryDark.withValues(alpha: 0.1)
                    : AppTheme.primaryLight.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 30.w,
                    height: 30.w,
                    decoration: BoxDecoration(
                      color:
                          isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: CustomIconWidget(
                        iconName: 'receipt_long',
                        color: Colors.white,
                        size: 15.w,
                      ),
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Container(
                    width: 40.w,
                    height: 2.h,
                    decoration: BoxDecoration(
                      color: isDark
                          ? AppTheme.surfaceVariantDark
                          : AppTheme.surfaceVariantLight,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  SizedBox(height: 2.h),
                  Container(
                    width: 30.w,
                    height: 2.h,
                    decoration: BoxDecoration(
                      color: isDark
                          ? AppTheme.surfaceVariantDark
                          : AppTheme.surfaceVariantLight,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  SizedBox(height: 2.h),
                  Container(
                    width: 35.w,
                    height: 2.h,
                    decoration: BoxDecoration(
                      color: isDark
                          ? AppTheme.surfaceVariantDark
                          : AppTheme.surfaceVariantLight,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: 6.h),

            // Title
            Text(
              'No Expenses Yet',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: isDark
                        ? AppTheme.onSurfaceDark
                        : AppTheme.onSurfaceLight,
                  ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 2.h),

            // Description
            Text(
              'Start tracking your group expenses by adding your first expense. Split bills, track payments, and manage your finances effortlessly.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: isDark
                        ? AppTheme.onSurfaceVariantDark
                        : AppTheme.onSurfaceVariantLight,
                    height: 1.5,
                  ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 6.h),

            // Add Expense Button
            SizedBox(
              width: 70.w,
              height: 6.h,
              child: ElevatedButton.icon(
                onPressed: onAddExpense,
                icon: CustomIconWidget(
                  iconName: 'add',
                  color: Colors.white,
                  size: 5.w,
                ),
                label: Text(
                  'Add Your First Expense',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),

            SizedBox(height: 4.h),

            // Secondary Actions
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                TextButton.icon(
                  onPressed: () {
                    Navigator.pushNamed(context, '/groups-list');
                  },
                  icon: CustomIconWidget(
                    iconName: 'group',
                    color:
                        isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
                    size: 4.w,
                  ),
                  label: Text(
                    'Browse Groups',
                    style: TextStyle(
                      color:
                          isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                SizedBox(width: 4.w),
                Container(
                  width: 1,
                  height: 4.h,
                  color: isDark ? AppTheme.outlineDark : AppTheme.outlineLight,
                ),
                SizedBox(width: 4.w),
                TextButton.icon(
                  onPressed: () {
                    Navigator.pushNamed(context, '/home-dashboard');
                  },
                  icon: CustomIconWidget(
                    iconName: 'dashboard',
                    color:
                        isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
                    size: 4.w,
                  ),
                  label: Text(
                    'Dashboard',
                    style: TextStyle(
                      color:
                          isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
