import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class CreateGroupFormWidget extends StatefulWidget {
  final VoidCallback? onGroupCreated;

  const CreateGroupFormWidget({
    Key? key,
    this.onGroupCreated,
  }) : super(key: key);

  @override
  State<CreateGroupFormWidget> createState() => _CreateGroupFormWidgetState();
}

class _CreateGroupFormWidgetState extends State<CreateGroupFormWidget>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _groupNameController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _memberEmailController = TextEditingController();

  List<Map<String, dynamic>> _members = [];
  String? _selectedGroupImage;
  bool _isLoading = false;

  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;

  // Predefined group images
  final List<String> _groupImages = [
    'https://images.pexels.com/photos/1450360/pexels-photo-1450360.jpeg?auto=compress&cs=tinysrgb&w=800',
    'https://images.pixabay.com/photos/2017/01/26/02/06/platter-2009590_1280.jpg',
    'https://images.unsplash.com/photo-1530103862676-de8c9debad1d?auto=format&fit=crop&w=800&q=80',
    'https://images.pexels.com/photos/3184292/pexels-photo-3184292.jpeg?auto=compress&cs=tinysrgb&w=800',
    'https://images.pixabay.com/photo/2016/11/29/03/36/beach-1867271_1280.jpg',
    'https://images.unsplash.com/photo-1502920917128-1aa500764cbd?auto=format&fit=crop&w=800&q=80',
  ];

  @override
  void initState() {
    super.initState();
    _slideController = AnimationController(
        duration: const Duration(milliseconds: 300), vsync: this);

    _slideAnimation = Tween<Offset>(begin: const Offset(0, 1), end: Offset.zero)
        .animate(CurvedAnimation(
            parent: _slideController, curve: Curves.easeOutCubic));

    // Start animation
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        _slideController.forward();
      }
    });

    // Add current user as default member
    _members.add({
      'id': 'current_user',
      'name': 'You',
      'email': '<EMAIL>',
      'avatar':
          'https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png',
      'isCurrentUser': true,
    });
  }

  @override
  void dispose() {
    _groupNameController.dispose();
    _descriptionController.dispose();
    _memberEmailController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  void _addMember() {
    final email = _memberEmailController.text.trim();
    if (email.isEmpty || !_isValidEmail(email)) {
      _showSnackBar('Please enter a valid email address');
      return;
    }

    // Check for duplicate emails
    final isDuplicate = _members.any((member) => member['email'] == email);
    if (isDuplicate) {
      _showSnackBar('Member already added');
      return;
    }

    setState(() {
      _members.add({
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'name': email.split('@')[0],
        'email': email,
        'avatar':
            'https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png',
        'isCurrentUser': false,
      });
    });

    _memberEmailController.clear();
    FocusScope.of(context).unfocus();
  }

  void _removeMember(String memberId) {
    setState(() {
      _members.removeWhere((member) => member['id'] == memberId);
    });
  }

  bool _isValidEmail(String email) {
    return RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(email);
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text(message),
        behavior: SnackBarBehavior.floating,
        shape:
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(12))));
  }

  Future<void> _createGroup() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_members.length < 2) {
      _showSnackBar('Please add at least one other member');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // Simulate API call
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _isLoading = false;
    });

    _showSnackBar('Group created successfully!');

    // Close the form
    Navigator.of(context).pop();

    // Callback to refresh groups list
    widget.onGroupCreated?.call();
  }

  void _showImagePicker() {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        builder: (context) => Container(
            height: 40.h,
            decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(20))),
            child: Column(children: [
              Container(
                  margin: EdgeInsets.only(top: 2.h),
                  width: 12.w,
                  height: 0.5.h,
                  decoration: BoxDecoration(
                      color: Theme.of(context)
                          .colorScheme
                          .onSurfaceVariant
                          .withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(2))),
              Padding(
                  padding: EdgeInsets.all(4.w),
                  child: Text('Choose Group Image',
                      style: Theme.of(context)
                          .textTheme
                          .titleLarge
                          ?.copyWith(fontWeight: FontWeight.w600))),
              Expanded(
                  child: GridView.builder(
                      padding: EdgeInsets.symmetric(horizontal: 4.w),
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 2,
                              crossAxisSpacing: 12,
                              mainAxisSpacing: 12,
                              childAspectRatio: 1.2),
                      itemCount: _groupImages.length,
                      itemBuilder: (context, index) {
                        final imageUrl = _groupImages[index];
                        final isSelected = _selectedGroupImage == imageUrl;

                        return GestureDetector(
                            onTap: () {
                              setState(() {
                                _selectedGroupImage = imageUrl;
                              });
                              Navigator.pop(context);
                            },
                            child: Container(
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(12),
                                    border: isSelected
                                        ? Border.all(
                                            color: AppTheme
                                                .lightTheme.primaryColor,
                                            width: 3)
                                        : null,
                                    boxShadow: [
                                      BoxShadow(
                                          color: Colors.black
                                              .withValues(alpha: 0.1),
                                          blurRadius: 8,
                                          offset: const Offset(0, 2)),
                                    ]),
                                child: ClipRRect(
                                    borderRadius: BorderRadius.circular(12),
                                    child:
                                        CustomImageWidget(imageUrl: imageUrl, fit: BoxFit.cover))));
                      })),
            ])));
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
        position: _slideAnimation,
        child: Container(
            height: 90.h,
            decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(20))),
            child: Column(children: [
              // Handle bar
              Container(
                  margin: EdgeInsets.only(top: 2.h),
                  width: 12.w,
                  height: 0.5.h,
                  decoration: BoxDecoration(
                      color: Theme.of(context)
                          .colorScheme
                          .onSurfaceVariant
                          .withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(2))),

              // Header
              Padding(
                  padding: EdgeInsets.all(4.w),
                  child: Row(children: [
                    GestureDetector(
                        onTap: () => Navigator.pop(context),
                        child: CustomIconWidget(
                            iconName: 'close',
                            size: 6.w,
                            color: Theme.of(context).colorScheme.onSurface)),
                    SizedBox(width: 4.w),
                    Expanded(
                        child: Text('Create New Group',
                            style: Theme.of(context)
                                .textTheme
                                .titleLarge
                                ?.copyWith(fontWeight: FontWeight.w600))),
                    if (_isLoading)
                      SizedBox(
                          width: 5.w,
                          height: 5.w,
                          child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: AppTheme.lightTheme.primaryColor)),
                  ])),

              Expanded(
                  child: Form(
                      key: _formKey,
                      child: SingleChildScrollView(
                          padding: EdgeInsets.symmetric(horizontal: 4.w),
                          child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(height: 2.h),

                                // Group Image Section
                                Center(
                                    child: GestureDetector(
                                        onTap: _showImagePicker,
                                        child: Container(
                                            width: 25.w,
                                            height: 25.w,
                                            decoration: BoxDecoration(
                                                color: AppTheme.lightTheme.primaryColor
                                                    .withValues(alpha: 0.1),
                                                borderRadius:
                                                    BorderRadius.circular(16),
                                                border: Border.all(
                                                    color: AppTheme
                                                        .lightTheme.primaryColor
                                                        .withValues(alpha: 0.3),
                                                    width: 2)),
                                            child: _selectedGroupImage != null
                                                ? ClipRRect(
                                                    borderRadius: BorderRadius.circular(
                                                        14),
                                                    child: CustomImageWidget(
                                                        imageUrl: _selectedGroupImage!,
                                                        fit: BoxFit.cover))
                                                : Column(
                                                    mainAxisAlignment: MainAxisAlignment.center,
                                                    children: [
                                                        CustomIconWidget(
                                                            iconName:
                                                                'add_a_photo',
                                                            size: 8.w,
                                                            color: AppTheme
                                                                .lightTheme
                                                                .primaryColor),
                                                        SizedBox(height: 1.h),
                                                        Text('Add Image',
                                                            style: Theme.of(
                                                                    context)
                                                                .textTheme
                                                                .bodySmall
                                                                ?.copyWith(
                                                                    color: AppTheme
                                                                        .lightTheme
                                                                        .primaryColor,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .w500)),
                                                      ])))),

                                SizedBox(height: 4.h),

                                // Group Name
                                Text('Group Name *',
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleMedium
                                        ?.copyWith(
                                            fontWeight: FontWeight.w600)),
                                SizedBox(height: 1.h),
                                TextFormField(
                                    controller: _groupNameController,
                                    decoration: const InputDecoration(
                                        hintText: 'Enter group name',
                                        prefixIcon: Icon(Icons.group)),
                                    validator: (value) {
                                      if (value == null ||
                                          value.trim().isEmpty) {
                                        return 'Group name is required';
                                      }
                                      if (value.trim().length < 3) {
                                        return 'Group name must be at least 3 characters';
                                      }
                                      return null;
                                    }),

                                SizedBox(height: 3.h),

                                // Description
                                Text('Description (Optional)',
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleMedium
                                        ?.copyWith(
                                            fontWeight: FontWeight.w600)),
                                SizedBox(height: 1.h),
                                TextFormField(
                                    controller: _descriptionController,
                                    maxLines: 3,
                                    decoration: const InputDecoration(
                                        hintText: 'What is this group for?',
                                        prefixIcon: Icon(Icons.description))),

                                SizedBox(height: 3.h),

                                // Members Section
                                Row(children: [
                                  Text('Members',
                                      style: Theme.of(context)
                                          .textTheme
                                          .titleMedium
                                          ?.copyWith(
                                              fontWeight: FontWeight.w600)),
                                  SizedBox(width: 2.w),
                                  Container(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 2.w, vertical: 0.5.h),
                                      decoration: BoxDecoration(
                                          color:
                                              AppTheme.lightTheme.primaryColor,
                                          borderRadius:
                                              BorderRadius.circular(12)),
                                      child: Text('${_members.length}',
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodySmall
                                              ?.copyWith(
                                                  color: Colors.white,
                                                  fontWeight:
                                                      FontWeight.w600))),
                                ]),

                                SizedBox(height: 1.h),

                                // Add Member Input
                                Row(children: [
                                  Expanded(
                                      child: TextFormField(
                                          controller: _memberEmailController,
                                          decoration: const InputDecoration(
                                              hintText:
                                                  'Enter email to add member',
                                              prefixIcon:
                                                  Icon(Icons.person_add)),
                                          keyboardType:
                                              TextInputType.emailAddress,
                                          onFieldSubmitted: (_) =>
                                              _addMember())),
                                  SizedBox(width: 2.w),
                                  GestureDetector(
                                      onTap: _addMember,
                                      child: Container(
                                          padding: EdgeInsets.all(3.w),
                                          decoration: BoxDecoration(
                                              color: AppTheme
                                                  .lightTheme.primaryColor,
                                              borderRadius:
                                                  BorderRadius.circular(12)),
                                          child: CustomIconWidget(
                                              iconName: 'add',
                                              size: 5.w,
                                              color: Colors.white))),
                                ]),

                                SizedBox(height: 2.h),

                                // Members List
                                if (_members.isNotEmpty) ...[
                                  Container(
                                      decoration: BoxDecoration(
                                          color: Theme.of(context)
                                              .colorScheme
                                              .surfaceContainerHighest,
                                          borderRadius:
                                              BorderRadius.circular(12)),
                                      child: Column(
                                          children: _members.map((member) {
                                        final isCurrentUser =
                                            member['isCurrentUser'] == true;
                                        return Container(
                                            padding: EdgeInsets.all(3.w),
                                            decoration: BoxDecoration(
                                                border: Border(
                                                    bottom: BorderSide(
                                                        color: Theme.of(context)
                                                            .colorScheme
                                                            .outline
                                                            .withValues(
                                                                alpha: 0.3),
                                                        width: 0.5))),
                                            child: Row(children: [
                                              CircleAvatar(
                                                  radius: 5.w,
                                                  backgroundImage: NetworkImage(
                                                      member['avatar'])),
                                              SizedBox(width: 3.w),
                                              Expanded(
                                                  child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                    Text(member['name'],
                                                        style: Theme.of(context)
                                                            .textTheme
                                                            .bodyMedium
                                                            ?.copyWith(
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w600)),
                                                    Text(member['email'],
                                                        style: Theme.of(context)
                                                            .textTheme
                                                            .bodySmall
                                                            ?.copyWith(
                                                                color: Theme.of(
                                                                        context)
                                                                    .colorScheme
                                                                    .onSurfaceVariant)),
                                                  ])),
                                              if (isCurrentUser)
                                                Container(
                                                    padding: EdgeInsets.symmetric(
                                                        horizontal: 2.w,
                                                        vertical: 0.5.h),
                                                    decoration: BoxDecoration(
                                                        color: AppTheme.lightTheme.primaryColor
                                                            .withValues(
                                                                alpha: 0.1),
                                                        borderRadius: BorderRadius.circular(
                                                            8)),
                                                    child: Text('Admin',
                                                        style: Theme.of(context)
                                                            .textTheme
                                                            .bodySmall
                                                            ?.copyWith(
                                                                color: AppTheme
                                                                    .lightTheme
                                                                    .primaryColor,
                                                                fontWeight: FontWeight
                                                                    .w600)))
                                              else
                                                GestureDetector(
                                                    onTap: () => _removeMember(member['id']),
                                                    child: CustomIconWidget(iconName: 'remove_circle', size: 5.w, color: Colors.red)),
                                            ]));
                                      }).toList())),
                                ],

                                SizedBox(height: 4.h),
                              ])))),

              // Create Button
              Container(
                  padding: EdgeInsets.all(4.w),
                  child: SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                          onPressed: _isLoading ? null : _createGroup,
                          style: ElevatedButton.styleFrom(
                              padding: EdgeInsets.symmetric(vertical: 4.w)),
                          child: _isLoading
                              ? SizedBox(
                                  height: 5.w,
                                  width: 5.w,
                                  child: const CircularProgressIndicator(
                                      strokeWidth: 2, color: Colors.white))
                              : Text('Create Group',
                                  style: Theme.of(context)
                                      .textTheme
                                      .titleMedium
                                      ?.copyWith(
                                          color: Colors.white,
                                          fontWeight: FontWeight.w600))))),
            ])));
  }
}